<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\EmployerVerification;
use App\Models\Employer;
use App\Models\User;
use App\Models\VerificationDocument;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EmployerVerificationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_render_the_employer_verification_component()
    {
        Livewire::test(EmployerVerification::class)
            ->assertStatus(200)
            ->assertSee('Employer Verification Management');
    }

    /** @test */
    public function it_can_filter_employers_by_status()
    {
        // Create test data
        $user = User::factory()->create();
        $verifiedEmployer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Verified Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $unverifiedEmployer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Unverified Company',
            'industry' => 'Healthcare',
            'is_verified' => false,
        ]);

        // Test filtering by approved status
        Livewire::test(EmployerVerification::class)
            ->set('statusFilter', 'approved')
            ->assertSee('Verified Company')
            ->assertDontSee('Unverified Company');
    }

    /** @test */
    public function it_can_search_employers()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Tech Solutions Inc',
            'industry' => 'Technology',
            'is_verified' => false,
        ]);

        Livewire::test(EmployerVerification::class)
            ->set('search', 'Tech Solutions')
            ->assertSee('Tech Solutions Inc');

        Livewire::test(EmployerVerification::class)
            ->set('search', 'NonExistent')
            ->assertDontSee('Tech Solutions Inc');
    }

    /** @test */
    public function it_can_approve_employer()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => false,
        ]);

        Livewire::test(EmployerVerification::class)
            ->call('approveEmployer', $employer->id)
            ->assertHasNoErrors();

        $this->assertTrue($employer->fresh()->is_verified);
    }

    /** @test */
    public function it_can_reject_employer()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        Livewire::test(EmployerVerification::class)
            ->call('rejectEmployer', $employer->id)
            ->assertHasNoErrors();

        $this->assertFalse($employer->fresh()->is_verified);
    }

    /** @test */
    public function it_can_show_document_modal()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => false,
        ]);

        Livewire::test(EmployerVerification::class)
            ->call('showDocuments', $employer->id)
            ->assertSet('showDocumentModal', true)
            ->assertSet('selectedEmployer.id', $employer->id);
    }

    /** @test */
    public function it_can_close_document_modal()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => false,
        ]);

        Livewire::test(EmployerVerification::class)
            ->call('showDocuments', $employer->id)
            ->assertSet('showDocumentModal', true)
            ->call('closeDocumentModal')
            ->assertSet('showDocumentModal', false)
            ->assertSet('selectedEmployer', null);
    }

    /** @test */
    public function it_can_process_document_verification()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => false,
        ]);

        $document = VerificationDocument::create([
            'employer_id' => $employer->id,
            'document_type' => 'business_license',
            'status' => 'pending',
            'submission_date' => now(),
        ]);

        Livewire::test(EmployerVerification::class)
            ->call('selectDocument', $document->id)
            ->set('verificationAction', 'approved')
            ->set('verificationNotes', 'Document looks good')
            ->call('processVerification')
            ->assertHasNoErrors();

        $this->assertEquals('approved', $document->fresh()->status);
        $this->assertEquals('Document looks good', $document->fresh()->notes);
    }
}
