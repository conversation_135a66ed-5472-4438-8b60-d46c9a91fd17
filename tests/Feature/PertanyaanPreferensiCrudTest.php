<?php

namespace Tests\Feature;

use App\Livewire\PertanyaanPreferensi;
use App\Models\PertanyaanPreferensi as PertanyaanPreferensiModel;
use App\Models\JawabanPreferensi;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class PertanyaanPreferensiCrudTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the migration manually for the test
        $this->artisan('migrate', ['--database' => 'testing']);
    }

    /** @test */
    public function it_can_render_the_pertanyaan_preferensi_component()
    {
        Livewire::test(PertanyaanPreferensi::class)
            ->assertStatus(200)
            ->assertSee('Pertanyaan Preferensi Management');
    }

    /** @test */
    public function it_can_create_a_new_pertanyaan()
    {
        Livewire::test(PertanyaanPreferensi::class)
            ->call('showCreateForm')
            ->set('teks_pertanyaan', 'What is your preferred work style?')
            ->set('tipe_pertanyaan', 'multiple_choice')
            ->set('kategori', 'professional')
            ->set('status', true)
            ->set('urutan', 1)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('showForm', false);

        $this->assertDatabaseHas('pertanyaan', [
            'teks_pertanyaan' => 'What is your preferred work style?',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_pertanyaan()
    {
        Livewire::test(PertanyaanPreferensi::class)
            ->call('showCreateForm')
            ->set('teks_pertanyaan', '')
            ->set('tipe_pertanyaan', '')
            ->set('kategori', '')
            ->call('save')
            ->assertHasErrors([
                'teks_pertanyaan' => 'required',
                'tipe_pertanyaan' => 'required',
                'kategori' => 'required'
            ]);
    }

    /** @test */
    public function it_can_edit_an_existing_pertanyaan()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Original question',
            'tipe_pertanyaan' => 'text',
            'kategori' => 'personal',
            'status' => true,
            'urutan' => 1,
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('showEditForm', $pertanyaan->id)
            ->assertSet('teks_pertanyaan', 'Original question')
            ->assertSet('tipe_pertanyaan', 'text')
            ->assertSet('kategori', 'personal')
            ->assertSet('flashEffect', true)
            ->set('teks_pertanyaan', 'Updated question')
            ->set('tipe_pertanyaan', 'multiple_choice')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas('pertanyaan', [
            'id' => $pertanyaan->id,
            'teks_pertanyaan' => 'Updated question',
            'tipe_pertanyaan' => 'multiple_choice',
        ]);
    }

    /** @test */
    public function it_can_delete_a_pertanyaan_with_related_jawaban()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Question to delete',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        // Create related jawaban
        JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Answer 1',
        ]);

        JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Answer 2',
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('confirmDelete', $pertanyaan->id)
            ->assertSet('confirmingDeletion', true)
            ->call('delete')
            ->assertSet('confirmingDeletion', false);

        $this->assertDatabaseMissing('pertanyaan', [
            'id' => $pertanyaan->id,
        ]);

        $this->assertDatabaseMissing('jawaban', [
            'id_pertanyaan' => $pertanyaan->id,
        ]);
    }

    /** @test */
    public function it_can_search_pertanyaan()
    {
        PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Technology question',
            'tipe_pertanyaan' => 'text',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Personal question',
            'tipe_pertanyaan' => 'text',
            'kategori' => 'personal',
            'status' => true,
            'urutan' => 2,
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->set('search', 'Technology')
            ->assertSee('Technology question')
            ->assertDontSee('Personal question');
    }

    /** @test */
    public function it_applies_flash_effect_only_for_edit_mode()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'text',
            'kategori' => 'personal',
            'status' => true,
            'urutan' => 1,
        ]);

        // Test edit mode has flash effect
        $component = Livewire::test(PertanyaanPreferensi::class)
            ->call('showEditForm', $pertanyaan->id)
            ->assertSet('flashEffect', true);

        // Test create mode does not have flash effect
        $component->call('showCreateForm')
            ->assertSet('flashEffect', false);
    }

    /** @test */
    public function it_includes_flash_effect_css_and_javascript()
    {
        $response = $this->get('/pertanyaan-preferensi');
        
        $response->assertStatus(200)
                 ->assertSee('winforms-edit-flash')
                 ->assertSee('@keyframes winforms-edit-flash')
                 ->assertSee('reset-flash-effect')
                 ->assertSee('MutationObserver');
    }
}
