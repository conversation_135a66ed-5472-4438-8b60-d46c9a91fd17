<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\JobPostingMonitor;
use App\Models\JobPosting;
use App\Models\Employer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class JobPostingMonitorTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_render_the_job_posting_monitor_component()
    {
        Livewire::test(JobPostingMonitor::class)
            ->assertStatus(200)
            ->assertSee('Job Posting Monitor');
    }

    /** @test */
    public function it_can_filter_jobs_by_status()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $activeJob = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Active Job',
            'description' => 'Active job description',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        $inactiveJob = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Inactive Job',
            'description' => 'Inactive job description',
            'location' => 'Surabaya',
            'job_type' => 'part-time',
            'is_active' => false,
        ]);

        // Test filtering by active status
        Livewire::test(JobPostingMonitor::class)
            ->set('statusFilter', 'active')
            ->assertSee('Active Job')
            ->assertDontSee('Inactive Job');

        // Test filtering by inactive status
        Livewire::test(JobPostingMonitor::class)
            ->set('statusFilter', 'inactive')
            ->assertSee('Inactive Job')
            ->assertDontSee('Active Job');
    }

    /** @test */
    public function it_can_search_jobs()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Tech Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $job = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Software Developer',
            'description' => 'Develop software applications',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->set('search', 'Software Developer')
            ->assertSee('Software Developer');

        Livewire::test(JobPostingMonitor::class)
            ->set('search', 'NonExistent')
            ->assertDontSee('Software Developer');
    }

    /** @test */
    public function it_can_filter_by_job_type()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $fullTimeJob = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Full Time Job',
            'description' => 'Full time position',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        $partTimeJob = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Part Time Job',
            'description' => 'Part time position',
            'location' => 'Jakarta',
            'job_type' => 'part-time',
            'is_active' => true,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->set('jobTypeFilter', 'full-time')
            ->assertSee('Full Time Job')
            ->assertDontSee('Part Time Job');
    }

    /** @test */
    public function it_can_filter_by_location()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $jakartaJob = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Jakarta Job',
            'description' => 'Job in Jakarta',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        $surabayaJob = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Surabaya Job',
            'description' => 'Job in Surabaya',
            'location' => 'Surabaya',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->set('locationFilter', 'Jakarta')
            ->assertSee('Jakarta Job')
            ->assertDontSee('Surabaya Job');
    }

    /** @test */
    public function it_can_activate_job()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $job = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Test Job',
            'description' => 'Test job description',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => false,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->call('activateJob', $job->id)
            ->assertHasNoErrors();

        $this->assertTrue($job->fresh()->is_active);
    }

    /** @test */
    public function it_can_deactivate_job()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $job = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Test Job',
            'description' => 'Test job description',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->call('deactivateJob', $job->id)
            ->assertHasNoErrors();

        $this->assertFalse($job->fresh()->is_active);
    }

    /** @test */
    public function it_can_show_job_details_modal()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $job = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Test Job',
            'description' => 'Test job description',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->call('showJobDetails', $job->id)
            ->assertSet('showJobModal', true)
            ->assertSet('selectedJob.id', $job->id);
    }

    /** @test */
    public function it_can_close_job_details_modal()
    {
        $user = User::factory()->create();
        $employer = Employer::create([
            'user_id' => $user->id,
            'company_name' => 'Test Company',
            'industry' => 'Technology',
            'is_verified' => true,
        ]);

        $job = JobPosting::create([
            'employer_id' => $employer->id,
            'title' => 'Test Job',
            'description' => 'Test job description',
            'location' => 'Jakarta',
            'job_type' => 'full-time',
            'is_active' => true,
        ]);

        Livewire::test(JobPostingMonitor::class)
            ->call('showJobDetails', $job->id)
            ->assertSet('showJobModal', true)
            ->call('closeJobModal')
            ->assertSet('showJobModal', false)
            ->assertSet('selectedJob', null);
    }
}
