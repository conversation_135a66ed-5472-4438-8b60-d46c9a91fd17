<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\VerificationDocument;
use App\Models\VerificationDocumentHistory;

class VerificationDocumentStatusConstantsTest extends TestCase
{
    public function test_verification_document_status_constants_are_defined()
    {
        $this->assertEquals('pending', VerificationDocument::STATUS_PENDING);
        $this->assertEquals('approved', VerificationDocument::STATUS_APPROVED);
        $this->assertEquals('rejected', VerificationDocument::STATUS_REJECTED);
        $this->assertEquals('incomplete', VerificationDocument::STATUS_INCOMPLETE);
        $this->assertEquals('missing', VerificationDocument::STATUS_MISSING);
        $this->assertEquals('submitted', VerificationDocument::STATUS_SUBMITTED);
        $this->assertEquals('under_review', VerificationDocument::STATUS_UNDER_REVIEW);
    }

    public function test_verification_document_history_status_constants_are_defined()
    {
        $this->assertEquals('pending', VerificationDocumentHistory::STATUS_PENDING);
        $this->assertEquals('approved', VerificationDocumentHistory::STATUS_APPROVED);
        $this->assertEquals('rejected', VerificationDocumentHistory::STATUS_REJECTED);
        $this->assertEquals('incomplete', VerificationDocumentHistory::STATUS_INCOMPLETE);
        $this->assertEquals('missing', VerificationDocumentHistory::STATUS_MISSING);
        $this->assertEquals('submitted', VerificationDocumentHistory::STATUS_SUBMITTED);
        $this->assertEquals('under_review', VerificationDocumentHistory::STATUS_UNDER_REVIEW);
    }

    public function test_verification_document_status_constants_match_history_constants()
    {
        $this->assertEquals(VerificationDocument::STATUS_PENDING, VerificationDocumentHistory::STATUS_PENDING);
        $this->assertEquals(VerificationDocument::STATUS_APPROVED, VerificationDocumentHistory::STATUS_APPROVED);
        $this->assertEquals(VerificationDocument::STATUS_REJECTED, VerificationDocumentHistory::STATUS_REJECTED);
        $this->assertEquals(VerificationDocument::STATUS_INCOMPLETE, VerificationDocumentHistory::STATUS_INCOMPLETE);
        $this->assertEquals(VerificationDocument::STATUS_MISSING, VerificationDocumentHistory::STATUS_MISSING);
        $this->assertEquals(VerificationDocument::STATUS_SUBMITTED, VerificationDocumentHistory::STATUS_SUBMITTED);
        $this->assertEquals(VerificationDocument::STATUS_UNDER_REVIEW, VerificationDocumentHistory::STATUS_UNDER_REVIEW);
    }

    public function test_get_status_options_returns_correct_array()
    {
        $expectedOptions = [
            'pending' => 'Pending Verification',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'incomplete' => 'Incomplete Documents',
            'missing' => 'Missing Documents',
            'submitted' => 'Submitted',
            'under_review' => 'Under Review',
        ];

        $this->assertEquals($expectedOptions, VerificationDocument::getStatusOptions());
        $this->assertEquals($expectedOptions, VerificationDocumentHistory::getStatusOptions());
    }

    public function test_get_status_display_name_returns_correct_values()
    {
        $this->assertEquals('Pending Verification', VerificationDocument::getStatusDisplayName('pending'));
        $this->assertEquals('Approved', VerificationDocument::getStatusDisplayName('approved'));
        $this->assertEquals('Rejected', VerificationDocument::getStatusDisplayName('rejected'));
        $this->assertEquals('Incomplete Documents', VerificationDocument::getStatusDisplayName('incomplete'));
        $this->assertEquals('Missing Documents', VerificationDocument::getStatusDisplayName('missing'));
        $this->assertEquals('Submitted', VerificationDocument::getStatusDisplayName('submitted'));
        $this->assertEquals('Under Review', VerificationDocument::getStatusDisplayName('under_review'));
        
        // Test fallback for unknown status
        $this->assertEquals('Unknown', VerificationDocument::getStatusDisplayName('unknown'));
    }

    public function test_status_constants_are_used_in_verification_workflow()
    {
        // Test that the constants match what's expected in the verification workflow
        $workflowStatuses = [
            VerificationDocument::STATUS_PENDING,
            VerificationDocument::STATUS_APPROVED,
            VerificationDocument::STATUS_REJECTED,
            VerificationDocument::STATUS_INCOMPLETE,
            VerificationDocument::STATUS_MISSING,
        ];

        foreach ($workflowStatuses as $status) {
            $this->assertIsString($status);
            $this->assertNotEmpty($status);
        }
    }

    public function test_status_constants_are_consistent_across_models()
    {
        $documentConstants = [
            VerificationDocument::STATUS_PENDING,
            VerificationDocument::STATUS_APPROVED,
            VerificationDocument::STATUS_REJECTED,
            VerificationDocument::STATUS_INCOMPLETE,
            VerificationDocument::STATUS_MISSING,
            VerificationDocument::STATUS_SUBMITTED,
            VerificationDocument::STATUS_UNDER_REVIEW,
        ];

        $historyConstants = [
            VerificationDocumentHistory::STATUS_PENDING,
            VerificationDocumentHistory::STATUS_APPROVED,
            VerificationDocumentHistory::STATUS_REJECTED,
            VerificationDocumentHistory::STATUS_INCOMPLETE,
            VerificationDocumentHistory::STATUS_MISSING,
            VerificationDocumentHistory::STATUS_SUBMITTED,
            VerificationDocumentHistory::STATUS_UNDER_REVIEW,
        ];

        $this->assertEquals($documentConstants, $historyConstants);
    }

    public function test_all_status_constants_have_display_names()
    {
        $constants = [
            VerificationDocument::STATUS_PENDING,
            VerificationDocument::STATUS_APPROVED,
            VerificationDocument::STATUS_REJECTED,
            VerificationDocument::STATUS_INCOMPLETE,
            VerificationDocument::STATUS_MISSING,
            VerificationDocument::STATUS_SUBMITTED,
            VerificationDocument::STATUS_UNDER_REVIEW,
        ];

        $statusOptions = VerificationDocument::getStatusOptions();

        foreach ($constants as $constant) {
            $this->assertArrayHasKey($constant, $statusOptions);
            $this->assertNotEmpty($statusOptions[$constant]);
        }
    }
}
