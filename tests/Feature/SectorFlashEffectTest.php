<?php

namespace Tests\Feature;

use App\Livewire\Sector;
use App\Models\Sector as SectorModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class SectorFlashEffectTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the migration manually for the test
        $this->artisan('migrate', ['--database' => 'testing']);
    }

    /** @test */
    public function it_applies_flash_effect_class_when_editing_sector()
    {
        // Create a sector to edit
        $sector = SectorModel::create([
            'sector' => 'Technology',
            'sector_desc' => 'Tech sector description',
            'is_active' => true,
        ]);

        // Test that flash effect is applied when editing
        $component = Livewire::test(Sector::class)
            ->call('showEditForm', $sector->sector_id)
            ->assertSet('editMode', true)
            ->assertSet('flashEffect', true)
            ->assertSee('winforms-form-flash')
            ->assertSee('winforms-form-title-flash');

        // Verify the form is populated with sector data
        $component->assertSet('sector', 'Technology')
                  ->assertSet('sector_desc', 'Tech sector description')
                  ->assertSet('is_active', true);
    }

    /** @test */
    public function it_does_not_apply_flash_effect_when_creating_new_sector()
    {
        // Test that flash effect is NOT applied when creating
        Livewire::test(Sector::class)
            ->call('showCreateForm')
            ->assertSet('editMode', false)
            ->assertSet('flashEffect', false)
            ->assertDontSee('winforms-form-flash')
            ->assertDontSee('winforms-form-title-flash');
    }

    /** @test */
    public function it_resets_flash_effect_when_form_is_reset()
    {
        $sector = SectorModel::create([
            'sector' => 'Healthcare',
            'sector_desc' => 'Healthcare sector',
            'is_active' => true,
        ]);

        // First trigger edit mode with flash effect
        $component = Livewire::test(Sector::class)
            ->call('showEditForm', $sector->sector_id)
            ->assertSet('flashEffect', true);

        // Then reset the form
        $component->call('resetForm')
                  ->assertSet('flashEffect', false)
                  ->assertSet('editMode', false);
    }

    /** @test */
    public function it_includes_flash_effect_css_animations()
    {
        $response = $this->get('/sectors');
        
        $response->assertStatus(200)
                 ->assertSee('winforms-edit-flash')
                 ->assertSee('@keyframes winforms-edit-flash')
                 ->assertSee('winforms-title-pulse')
                 ->assertSee('@keyframes winforms-title-pulse');
    }

    /** @test */
    public function it_includes_flash_effect_javascript()
    {
        $response = $this->get('/sectors');
        
        $response->assertStatus(200)
                 ->assertSee('reset-flash-effect')
                 ->assertSee('winforms-form-flash')
                 ->assertSee('MutationObserver');
    }
}
