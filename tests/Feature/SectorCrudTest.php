<?php

namespace Tests\Feature;

use App\Livewire\Sector;
use App\Models\Sector as SectorModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class SectorCrudTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_render_the_sector_component()
    {
        Livewire::test(Sector::class)
            ->assertStatus(200)
            ->assertSee('Sector Management');
    }

    /** @test */
    public function it_can_create_a_new_sector()
    {
        Livewire::test(Sector::class)
            ->call('showCreateForm')
            ->set('sector', 'Technology')
            ->set('sector_desc', 'Technology sector description')
            ->set('is_active', true)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('showForm', false);

        $this->assertDatabaseHas('sectors', [
            'sector' => 'Technology',
            'sector_desc' => 'Technology sector description',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_sector()
    {
        Livewire::test(Sector::class)
            ->call('showCreateForm')
            ->set('sector', '')
            ->call('save')
            ->assertHasErrors(['sector' => 'required']);
    }

    /** @test */
    public function it_can_edit_an_existing_sector()
    {
        $sector = SectorModel::create([
            'sector' => 'Healthcare',
            'sector_desc' => 'Healthcare sector',
            'is_active' => true,
        ]);

        Livewire::test(Sector::class)
            ->call('showEditForm', $sector->sector_id)
            ->assertSet('sector', 'Healthcare')
            ->assertSet('sector_desc', 'Healthcare sector')
            ->assertSet('is_active', true)
            ->set('sector', 'Healthcare Updated')
            ->set('sector_desc', 'Updated healthcare sector description')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas('sectors', [
            'sector_id' => $sector->sector_id,
            'sector' => 'Healthcare Updated',
            'sector_desc' => 'Updated healthcare sector description',
        ]);
    }

    /** @test */
    public function it_can_delete_a_sector()
    {
        $sector = SectorModel::create([
            'sector' => 'Finance',
            'sector_desc' => 'Finance sector',
            'is_active' => true,
        ]);

        Livewire::test(Sector::class)
            ->call('confirmDelete', $sector->sector_id)
            ->assertSet('confirmingDeletion', true)
            ->call('delete')
            ->assertSet('confirmingDeletion', false);

        $this->assertDatabaseMissing('sectors', [
            'sector_id' => $sector->sector_id,
        ]);
    }

    /** @test */
    public function it_can_search_sectors()
    {
        SectorModel::create(['sector' => 'Technology', 'sector_desc' => 'Tech sector', 'is_active' => true]);
        SectorModel::create(['sector' => 'Healthcare', 'sector_desc' => 'Health sector', 'is_active' => true]);
        SectorModel::create(['sector' => 'Finance', 'sector_desc' => 'Financial sector', 'is_active' => true]);

        Livewire::test(Sector::class)
            ->set('search', 'Tech')
            ->assertSee('Technology')
            ->assertDontSee('Healthcare')
            ->assertDontSee('Finance');
    }

    /** @test */
    public function it_prevents_duplicate_sector_names()
    {
        SectorModel::create([
            'sector' => 'Technology',
            'sector_desc' => 'Tech sector',
            'is_active' => true,
        ]);

        Livewire::test(Sector::class)
            ->call('showCreateForm')
            ->set('sector', 'Technology')
            ->set('sector_desc', 'Another tech sector')
            ->call('save')
            ->assertHasErrors(['sector']);
    }

    /** @test */
    public function it_can_cancel_form_operations()
    {
        Livewire::test(Sector::class)
            ->call('showCreateForm')
            ->assertSet('showForm', true)
            ->set('sector', 'Test Sector')
            ->call('cancel')
            ->assertSet('showForm', false)
            ->assertSet('sector', '');
    }

    /** @test */
    public function it_can_cancel_delete_confirmation()
    {
        $sector = SectorModel::create([
            'sector' => 'Education',
            'sector_desc' => 'Education sector',
            'is_active' => true,
        ]);

        Livewire::test(Sector::class)
            ->call('confirmDelete', $sector->sector_id)
            ->assertSet('confirmingDeletion', true)
            ->call('cancelDelete')
            ->assertSet('confirmingDeletion', false)
            ->assertSet('sectorToDelete', null);

        $this->assertDatabaseHas('sectors', [
            'sector_id' => $sector->sector_id,
        ]);
    }
}
