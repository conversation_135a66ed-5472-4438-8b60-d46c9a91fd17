<?php

namespace Tests\Feature;

use App\Livewire\Sector;
use App\Models\Sector as SectorModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class SectorCustomPaginationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the migration manually for the test
        $this->artisan('migrate', ['--database' => 'testing']);
    }

    /** @test */
    public function it_displays_custom_pagination_when_there_are_multiple_pages()
    {
        // Create 15 sectors to ensure pagination (10 per page)
        for ($i = 1; $i <= 15; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        $response = $this->get('/sectors');
        
        $response->assertStatus(200)
                 ->assertSee('winforms-custom-pagination')
                 ->assertSee('Page 1 of 2')
                 ->assertSee('(15 total records)')
                 ->assertSee('Showing 1 to 10 of 15 entries');
    }

    /** @test */
    public function it_includes_custom_pagination_styles()
    {
        $response = $this->get('/sectors');
        
        $response->assertStatus(200)
                 ->assertSee('winforms-pagination-container')
                 ->assertSee('winforms-pagination-btn')
                 ->assertSee('winforms-page-info')
                 ->assertSee('winforms-records-info');
    }

    /** @test */
    public function it_can_navigate_to_specific_page()
    {
        // Create 25 sectors to have multiple pages
        for ($i = 1; $i <= 25; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        Livewire::test(Sector::class)
            ->call('goToPage', 2)
            ->assertSee('Test Sector 11') // Should see sectors 11-20 on page 2
            ->assertDontSee('Test Sector 1'); // Should not see sector 1 on page 2
    }

    /** @test */
    public function it_can_navigate_to_previous_page()
    {
        // Create 15 sectors
        for ($i = 1; $i <= 15; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        Livewire::test(Sector::class)
            ->call('goToPage', 2)
            ->call('goPreviousPage')
            ->assertSee('Test Sector 1'); // Should be back on page 1
    }

    /** @test */
    public function it_can_navigate_to_next_page()
    {
        // Create 15 sectors
        for ($i = 1; $i <= 15; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        Livewire::test(Sector::class)
            ->call('goNextPage')
            ->assertSee('Test Sector 11') // Should see sectors 11-15 on page 2
            ->assertDontSee('Test Sector 1'); // Should not see sector 1 on page 2
    }

    /** @test */
    public function it_shows_correct_page_numbers_in_pagination_controls()
    {
        // Create 50 sectors to have multiple pages
        for ($i = 1; $i <= 50; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        $response = $this->get('/sectors');
        
        $response->assertStatus(200)
                 ->assertSee('Page 1 of 5') // Should show correct page info
                 ->assertSee('(50 total records)');
    }

    /** @test */
    public function it_disables_navigation_buttons_appropriately()
    {
        // Create just 5 sectors (single page)
        for ($i = 1; $i <= 5; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        $response = $this->get('/sectors');
        
        // Should not show pagination controls when there's only one page
        $response->assertStatus(200)
                 ->assertDontSee('winforms-pagination-container');
    }

    /** @test */
    public function it_shows_ellipsis_for_large_page_ranges()
    {
        // Create 100 sectors to have many pages
        for ($i = 1; $i <= 100; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        // Go to page 5 to see ellipsis
        Livewire::test(Sector::class)
            ->call('goToPage', 5)
            ->assertSee('winforms-pagination-ellipsis');
    }

    /** @test */
    public function it_maintains_search_functionality_with_custom_pagination()
    {
        // Create sectors with different names
        SectorModel::create(['sector' => 'Technology Sector', 'sector_desc' => 'Tech description', 'is_active' => true]);
        SectorModel::create(['sector' => 'Healthcare Sector', 'sector_desc' => 'Health description', 'is_active' => true]);
        
        // Create more sectors to test pagination with search
        for ($i = 1; $i <= 15; $i++) {
            SectorModel::create([
                'sector' => "Other Sector {$i}",
                'sector_desc' => "Other description {$i}",
                'is_active' => true,
            ]);
        }

        Livewire::test(Sector::class)
            ->set('search', 'Technology')
            ->assertSee('Technology Sector')
            ->assertDontSee('Healthcare Sector')
            ->assertDontSee('Other Sector 1');
    }

    /** @test */
    public function it_includes_first_and_last_page_buttons()
    {
        // Create 30 sectors to have multiple pages
        for ($i = 1; $i <= 30; $i++) {
            SectorModel::create([
                'sector' => "Test Sector {$i}",
                'sector_desc' => "Description for sector {$i}",
                'is_active' => true,
            ]);
        }

        $response = $this->get('/sectors');
        
        $response->assertStatus(200)
                 ->assertSee('First')
                 ->assertSee('Last')
                 ->assertSee('Prev')
                 ->assertSee('Next');
    }
}
