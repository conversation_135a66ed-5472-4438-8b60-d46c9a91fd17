<?php

namespace Tests\Feature;

use App\Livewire\PertanyaanPreferensi;
use App\Models\PertanyaanPreferensi as PertanyaanPreferensiModel;
use App\Models\JawabanPreferensi;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class PertanyaanPreferensiMasterDetailTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the migration manually for the test
        $this->artisan('migrate', ['--database' => 'testing']);
    }

    /** @test */
    public function it_can_toggle_expandable_rows()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->assertSet('expandedRows', [])
            ->call('toggleRow', $pertanyaan->id)
            ->assertSet('expandedRows', [$pertanyaan->id])
            ->call('toggleRow', $pertanyaan->id)
            ->assertSet('expandedRows', []);
    }

    /** @test */
    public function it_can_show_and_hide_add_answer_form()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        $component = Livewire::test(PertanyaanPreferensi::class)
            ->call('showAddAnswerForm', $pertanyaan->id)
            ->assertSet("showAnswerForm.{$pertanyaan->id}", true)
            ->assertSet("newAnswerText.{$pertanyaan->id}", '');

        $component->call('hideAddAnswerForm', $pertanyaan->id)
                  ->assertNotSet("showAnswerForm.{$pertanyaan->id}")
                  ->assertNotSet("newAnswerText.{$pertanyaan->id}");
    }

    /** @test */
    public function it_can_create_new_answer_for_question()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('showAddAnswerForm', $pertanyaan->id)
            ->set("newAnswerText.{$pertanyaan->id}", 'New answer text')
            ->call('saveNewAnswer', $pertanyaan->id)
            ->assertHasNoErrors();

        $this->assertDatabaseHas('jawaban', [
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'New answer text',
        ]);
    }

    /** @test */
    public function it_validates_new_answer_text()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('showAddAnswerForm', $pertanyaan->id)
            ->set("newAnswerText.{$pertanyaan->id}", '')
            ->call('saveNewAnswer', $pertanyaan->id)
            ->assertHasErrors(["newAnswerText.{$pertanyaan->id}" => 'required']);
    }

    /** @test */
    public function it_can_edit_existing_answer()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        $answer = JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Original answer',
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('editAnswer', $answer->id)
            ->assertSet("editingAnswer.{$answer->id}", true)
            ->assertSet("editAnswerText.{$answer->id}", 'Original answer')
            ->set("editAnswerText.{$answer->id}", 'Updated answer')
            ->call('updateAnswer', $answer->id)
            ->assertHasNoErrors();

        $this->assertDatabaseHas('jawaban', [
            'id' => $answer->id,
            'teks_jawaban' => 'Updated answer',
        ]);
    }

    /** @test */
    public function it_can_cancel_answer_editing()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        $answer = JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Original answer',
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('editAnswer', $answer->id)
            ->assertSet("editingAnswer.{$answer->id}", true)
            ->call('cancelEditAnswer', $answer->id)
            ->assertNotSet("editingAnswer.{$answer->id}")
            ->assertNotSet("editAnswerText.{$answer->id}");
    }

    /** @test */
    public function it_can_delete_answer_with_confirmation()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        $answer = JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Answer to delete',
        ]);

        Livewire::test(PertanyaanPreferensi::class)
            ->call('confirmDeleteAnswer', $answer->id)
            ->assertSet('confirmingAnswerDeletion', true)
            ->call('deleteAnswer')
            ->assertSet('confirmingAnswerDeletion', false);

        $this->assertDatabaseMissing('jawaban', [
            'id' => $answer->id,
        ]);
    }

    /** @test */
    public function it_displays_answer_count_in_main_grid()
    {
        $pertanyaan = PertanyaanPreferensiModel::create([
            'teks_pertanyaan' => 'Test question with answers',
            'tipe_pertanyaan' => 'multiple_choice',
            'kategori' => 'professional',
            'status' => true,
            'urutan' => 1,
        ]);

        // Create multiple answers
        JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Answer 1',
        ]);

        JawabanPreferensi::create([
            'id_pertanyaan' => $pertanyaan->id,
            'teks_jawaban' => 'Answer 2',
        ]);

        $response = $this->get('/pertanyaan-preferensi');
        
        $response->assertStatus(200)
                 ->assertSee('winforms-answer-count')
                 ->assertSee('2'); // Should show count of 2 answers
    }

    /** @test */
    public function it_includes_master_detail_interface_styles()
    {
        $response = $this->get('/pertanyaan-preferensi');
        
        $response->assertStatus(200)
                 ->assertSee('winforms-expandable-row')
                 ->assertSee('winforms-detail-row')
                 ->assertSee('winforms-answer-item')
                 ->assertSee('winforms-answer-form')
                 ->assertSee('winforms-expand-icon');
    }
}
