<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Livewire\EmployerVerification;
use App\Http\Controllers\DocumentController;

class ExternalUrlHandlingTest extends TestCase
{
    /** @test */
    public function it_can_detect_google_cloud_storage_urls()
    {
        $component = new EmployerVerification();
        
        // Test Google Cloud Storage URL detection
        $this->assertTrue($component->isExternalUrl('https://storage.googleapis.com/carikerja/carikerja-company/employers/pt-angkasa-pura/document.pdf'));
        $this->assertTrue($component->isExternalUrl('https://storage.googleapis.com/carikerja/carikerja-company/employers/pt-angkasa-pura/image.jpeg'));
    }

    /** @test */
    public function it_can_detect_other_external_urls()
    {
        $component = new EmployerVerification();
        
        // Test other external URLs
        $this->assertTrue($component->isExternalUrl('https://example.com/document.pdf'));
        $this->assertTrue($component->isExternalUrl('http://example.com/document.pdf'));
        $this->assertTrue($component->isExternalUrl('https://cdn.example.com/files/document.pdf'));
    }

    /** @test */
    public function it_can_detect_local_paths()
    {
        $component = new EmployerVerification();
        
        // Test local paths
        $this->assertFalse($component->isExternalUrl('uploads/documents/document.pdf'));
        $this->assertFalse($component->isExternalUrl('document.pdf'));
        $this->assertFalse($component->isExternalUrl('/storage/uploads/document.pdf'));
        $this->assertFalse($component->isExternalUrl('storage/app/public/uploads/document.pdf'));
    }

    /** @test */
    public function it_handles_empty_or_null_paths()
    {
        $component = new EmployerVerification();
        
        // Test empty/null values
        $this->assertFalse($component->isExternalUrl(''));
        $this->assertFalse($component->isExternalUrl(null));
    }

    /** @test */
    public function it_can_get_document_type_from_external_urls()
    {
        $component = new EmployerVerification();
        
        // Test document type detection from external URLs
        $this->assertEquals('pdf', $component->getDocumentType('https://storage.googleapis.com/carikerja/document.pdf'));
        $this->assertEquals('image', $component->getDocumentType('https://storage.googleapis.com/carikerja/image.jpeg'));
        $this->assertEquals('image', $component->getDocumentType('https://storage.googleapis.com/carikerja/image.png'));
        $this->assertEquals('document', $component->getDocumentType('https://storage.googleapis.com/carikerja/document.docx'));
    }

    /** @test */
    public function document_controller_can_detect_external_urls()
    {
        $controller = new DocumentController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('isExternalUrl');
        $method->setAccessible(true);
        
        // Test Google Cloud Storage URL detection
        $this->assertTrue($method->invoke($controller, 'https://storage.googleapis.com/carikerja/carikerja-company/employers/pt-angkasa-pura/document.pdf'));
        
        // Test other external URLs
        $this->assertTrue($method->invoke($controller, 'https://example.com/document.pdf'));
        $this->assertTrue($method->invoke($controller, 'http://example.com/document.pdf'));
        
        // Test local paths
        $this->assertFalse($method->invoke($controller, 'uploads/documents/document.pdf'));
        $this->assertFalse($method->invoke($controller, 'document.pdf'));
        $this->assertFalse($method->invoke($controller, ''));
        $this->assertFalse($method->invoke($controller, null));
    }

    /** @test */
    public function document_controller_can_get_mime_type_from_extension()
    {
        $controller = new DocumentController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('getMimeTypeFromExtension');
        $method->setAccessible(true);
        
        // Test common file types
        $this->assertEquals('application/pdf', $method->invoke($controller, 'pdf'));
        $this->assertEquals('image/jpeg', $method->invoke($controller, 'jpg'));
        $this->assertEquals('image/jpeg', $method->invoke($controller, 'jpeg'));
        $this->assertEquals('image/png', $method->invoke($controller, 'png'));
        $this->assertEquals('application/msword', $method->invoke($controller, 'doc'));
        $this->assertEquals('application/vnd.openxmlformats-officedocument.wordprocessingml.document', $method->invoke($controller, 'docx'));
        
        // Test unknown extension
        $this->assertEquals('application/octet-stream', $method->invoke($controller, 'unknown'));
    }

    /** @test */
    public function external_url_validation_handles_network_errors_gracefully()
    {
        $component = new EmployerVerification();
        $reflection = new \ReflectionClass($component);
        $method = $reflection->getMethod('validateExternalUrl');
        $method->setAccessible(true);
        
        // Test with a non-existent domain (should return false)
        $result = $method->invoke($component, 'https://this-domain-definitely-does-not-exist-12345.com/document.pdf');
        $this->assertFalse($result);
        
        // Test with malformed URL (should return false)
        $result = $method->invoke($component, 'not-a-valid-url');
        $this->assertFalse($result);
    }

    /** @test */
    public function external_url_patterns_are_correctly_identified()
    {
        $component = new EmployerVerification();

        // Test various Google Cloud Storage URL patterns
        $googleUrls = [
            'https://storage.googleapis.com/bucket/file.pdf',
            'https://storage.googleapis.com/carikerja/carikerja-company/employers/company-name/document.pdf',
            'https://storage.googleapis.com/carikerja/carikerja-company/employers/pt-angkasa-pura/business_license.jpeg',
            'https://storage.googleapis.com/another-bucket/subfolder/file.png'
        ];

        foreach ($googleUrls as $url) {
            $this->assertTrue($component->isExternalUrl($url), "Failed to detect Google Cloud Storage URL: $url");
        }

        // Test other cloud storage patterns
        $otherUrls = [
            'https://s3.amazonaws.com/bucket/file.pdf',
            'https://cdn.example.com/files/document.pdf',
            'https://files.example.com/uploads/image.jpg'
        ];

        foreach ($otherUrls as $url) {
            $this->assertTrue($component->isExternalUrl($url), "Failed to detect external URL: $url");
        }
    }

    /** @test */
    public function process_verification_method_exists_and_is_callable()
    {
        $component = new EmployerVerification();

        // Test that the method exists
        $this->assertTrue(method_exists($component, 'processVerification'));

        // Test that the method is public (callable)
        $reflection = new \ReflectionMethod($component, 'processVerification');
        $this->assertTrue($reflection->isPublic());
    }
}
