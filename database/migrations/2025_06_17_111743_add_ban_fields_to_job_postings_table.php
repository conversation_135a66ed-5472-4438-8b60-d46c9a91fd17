<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_postings', function (Blueprint $table) {
            $table->boolean('is_banned')->default(false)->after('is_active');
            $table->text('banned_reason')->nullable()->after('is_banned');
            $table->timestamp('banned_at')->nullable()->after('banned_reason');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_postings', function (Blueprint $table) {
            $table->dropColumn(['is_banned', 'banned_reason', 'banned_at']);
        });
    }
};
