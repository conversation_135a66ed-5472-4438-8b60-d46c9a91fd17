<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Employer;
use App\Models\JobPosting;
use App\Models\VerificationDocument;
use App\Models\VerificationDocumentHistory;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test users
        $users = [
            [
                'name' => 'Tech Corp Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
            [
                'name' => 'Healthcare Solutions',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
            [
                'name' => 'Finance Plus',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
            [
                'name' => 'Education Hub',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
        ];

        foreach ($users as $userData) {
            $user = User::create($userData);

            // Create employer profile
            $employer = Employer::create([
                'user_id' => $user->id,
                'company_name' => $this->getCompanyName($user->name),
                'company_description' => $this->getCompanyDescription($user->name),
                'industry' => $this->getIndustry($user->name),
                'website' => 'https://www.' . strtolower(str_replace(' ', '', $user->name)) . '.com',
                'phone' => '+62' . rand(**********, **********),
                'address' => $this->getAddress(),
                'city' => $this->getCity(),
                'province' => 'DKI Jakarta',
                'is_verified' => rand(0, 1) == 1,
            ]);

            // Create verification documents
            $documentTypes = ['business_license', 'tax_certificate', 'company_profile'];
            foreach ($documentTypes as $docType) {
                $document = VerificationDocument::create([
                    'employer_id' => $employer->id,
                    'document_type' => $docType,
                    'status' => $this->getRandomStatus(),
                    'submission_date' => now()->subDays(rand(1, 30)),
                    'verification_date' => rand(0, 1) ? now()->subDays(rand(0, 10)) : null,
                    'notes' => $this->getVerificationNotes(),
                    'verified_by' => rand(0, 1) ? 1 : null,
                ]);

                // Create document history
                VerificationDocumentHistory::create([
                    'verification_document_id' => $document->id,
                    'file_path' => '/uploads/documents/' . $docType . '_' . $employer->id . '.pdf',
                    'status_at_upload' => 'pending',
                    'notes' => 'Document uploaded by employer',
                    'verified_by' => null,
                    'verified_at' => null,
                ]);
            }

            // Create job postings
            $jobCount = rand(2, 5);
            for ($i = 0; $i < $jobCount; $i++) {
                JobPosting::create([
                    'employer_id' => $employer->id,
                    'title' => $this->getJobTitle($employer->industry),
                    'description' => $this->getJobDescription(),
                    'location' => $this->getJobLocation(),
                    'job_type' => $this->getJobType(),
                    'is_remote' => rand(0, 1) == 1,
                    'min_salary' => rand(5000000, 15000000),
                    'max_salary' => rand(15000000, 30000000),
                    'currency' => 'IDR',
                    'is_salary_displayed' => rand(0, 1) == 1,
                    'hide_salary' => rand(0, 1) == 1,
                    'application_deadline' => rand(0, 1) ? now()->addDays(rand(7, 60)) : null,
                    'is_active' => rand(0, 1) == 1,
                    'views_count' => rand(50, 1000),
                    'applicants_count' => rand(5, 100),
                    'qualifications' => $this->getQualifications(),
                    'benefits' => $this->getBenefits(),
                    'responsibilities' => $this->getResponsibilities(),
                ]);
            }
        }
    }

    private function getCompanyName($userName)
    {
        $companies = [
            'Tech Corp Admin' => 'TechCorp Indonesia',
            'Healthcare Solutions' => 'HealthCare Solutions Ltd',
            'Finance Plus' => 'Finance Plus Group',
            'Education Hub' => 'Education Hub Academy',
        ];

        return $companies[$userName] ?? $userName . ' Company';
    }

    private function getCompanyDescription($userName)
    {
        $descriptions = [
            'Tech Corp Admin' => 'Leading technology solutions provider in Indonesia',
            'Healthcare Solutions' => 'Comprehensive healthcare services and medical solutions',
            'Finance Plus' => 'Financial services and investment management company',
            'Education Hub' => 'Educational technology and learning solutions provider',
        ];

        return $descriptions[$userName] ?? 'Professional services company';
    }

    private function getIndustry($userName)
    {
        $industries = [
            'Tech Corp Admin' => 'Information Technology',
            'Healthcare Solutions' => 'Healthcare & Medical',
            'Finance Plus' => 'Financial Services',
            'Education Hub' => 'Education & Training',
        ];

        return $industries[$userName] ?? 'General Business';
    }

    private function getAddress()
    {
        $addresses = [
            'Jl. Sudirman No. 123, Jakarta Pusat',
            'Jl. Thamrin No. 456, Jakarta Pusat',
            'Jl. Kuningan No. 789, Jakarta Selatan',
            'Jl. Gatot Subroto No. 321, Jakarta Selatan',
        ];

        return $addresses[array_rand($addresses)];
    }

    private function getCity()
    {
        $cities = ['Jakarta Pusat', 'Jakarta Selatan', 'Jakarta Utara', 'Jakarta Barat', 'Jakarta Timur'];
        return $cities[array_rand($cities)];
    }

    private function getRandomStatus()
    {
        $statuses = ['pending', 'approved', 'rejected'];
        return $statuses[array_rand($statuses)];
    }

    private function getVerificationNotes()
    {
        $notes = [
            'Document is clear and complete',
            'Please resubmit with better quality',
            'Additional information required',
            'Document approved for verification',
            'Missing required signatures',
        ];

        return $notes[array_rand($notes)];
    }

    private function getJobTitle($industry)
    {
        $titles = [
            'Information Technology' => ['Software Developer', 'System Administrator', 'Data Analyst', 'DevOps Engineer'],
            'Healthcare & Medical' => ['Nurse', 'Medical Assistant', 'Healthcare Administrator', 'Pharmacist'],
            'Financial Services' => ['Financial Analyst', 'Account Manager', 'Investment Advisor', 'Risk Manager'],
            'Education & Training' => ['Teacher', 'Training Coordinator', 'Curriculum Developer', 'Education Consultant'],
        ];

        $industryTitles = $titles[$industry] ?? ['General Manager', 'Operations Specialist', 'Customer Service'];
        return $industryTitles[array_rand($industryTitles)];
    }

    private function getJobDescription()
    {
        return 'We are looking for a qualified professional to join our dynamic team. The ideal candidate will have relevant experience and strong communication skills.';
    }

    private function getJobLocation()
    {
        $locations = ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang', 'Yogyakarta'];
        return $locations[array_rand($locations)];
    }

    private function getJobType()
    {
        $types = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];
        return $types[array_rand($types)];
    }

    private function getQualifications()
    {
        return 'Bachelor degree in relevant field, minimum 2 years experience, good communication skills, proficient in English.';
    }

    private function getBenefits()
    {
        return 'Health insurance, annual leave, performance bonus, professional development opportunities.';
    }

    private function getResponsibilities()
    {
        return 'Manage daily operations, coordinate with team members, prepare reports, maintain quality standards.';
    }
}
