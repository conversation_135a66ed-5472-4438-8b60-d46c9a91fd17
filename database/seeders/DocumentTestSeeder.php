<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use App\Models\VerificationDocument;
use App\Models\VerificationDocumentHistory;
use App\Models\Employer;

class DocumentTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create uploads directory if it doesn't exist
        $uploadsPath = 'uploads/documents';
        if (!Storage::disk('public')->exists($uploadsPath)) {
            Storage::disk('public')->makeDirectory($uploadsPath);
        }

        // Create some test documents for existing employers
        $employers = Employer::take(3)->get();

        foreach ($employers as $employer) {
            // Create test documents for each employer
            $documentTypes = ['business_license', 'tax_certificate', 'company_profile'];
            
            foreach ($documentTypes as $index => $docType) {
                // Create a simple test file first
                $fileName = $docType . '_' . $employer->id . '_test.txt';
                $filePath = $uploadsPath . '/' . $fileName;
                
                $testContent = "TEST DOCUMENT\n\n";
                $testContent .= "Document Type: " . ucfirst(str_replace('_', ' ', $docType)) . "\n";
                $testContent .= "Employer: " . $employer->company_name . "\n";
                $testContent .= "Generated: " . now()->format('Y-m-d H:i:s') . "\n\n";
                $testContent .= "This is a test document created for preview functionality testing.\n";
                $testContent .= "In a real application, this would be a PDF or image file uploaded by the employer.\n\n";
                $testContent .= "Status: pending\n";

                // Save the test file
                Storage::disk('public')->put($filePath, $testContent);

                // Create verification document
                $document = VerificationDocument::create([
                    'employer_id' => $employer->id,
                    'document_type' => $docType,
                    'file_path' => $filePath,
                    'status' => VerificationDocument::STATUS_SUBMITTED,
                    'submission_date' => now()->subDays(rand(1, 30)),
                    'notes' => 'Test document for preview functionality',
                ]);

                // Create document history
                VerificationDocumentHistory::create([
                    'verification_document_id' => $document->id,
                    'file_path' => $filePath,
                    'status' => VerificationDocument::STATUS_SUBMITTED,
                    'notes' => 'Test document uploaded for preview functionality',
                    'upload_at' => now()->subDays(rand(1, 15)),
                ]);
            }
        }

        // Create a sample PDF-like document (actually HTML that looks like PDF)
        $firstEmployer = $employers->first();
        if ($firstEmployer) {

            $htmlContent = '<!DOCTYPE html>
<html>
<head>
    <title>Business Registration Certificate</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: white; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .title { font-size: 24px; font-weight: bold; color: #333; }
        .subtitle { font-size: 16px; color: #666; margin-top: 10px; }
        .content { line-height: 1.6; }
        .field { margin: 15px 0; }
        .label { font-weight: bold; display: inline-block; width: 200px; }
        .value { color: #333; }
        .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">BUSINESS REGISTRATION CERTIFICATE</div>
        <div class="subtitle">Republic of Indonesia</div>
    </div>
    
    <div class="content">
        <div class="field">
            <span class="label">Company Name:</span>
            <span class="value">' . $firstEmployer->company_name . '</span>
        </div>
        
        <div class="field">
            <span class="label">Registration Number:</span>
            <span class="value">REG-' . str_pad($firstEmployer->id, 6, '0', STR_PAD_LEFT) . '</span>
        </div>
        
        <div class="field">
            <span class="label">Industry:</span>
            <span class="value">' . ($firstEmployer->industry ?? 'General Business') . '</span>
        </div>
        
        <div class="field">
            <span class="label">Registration Date:</span>
            <span class="value">' . $firstEmployer->created_at->format('d F Y') . '</span>
        </div>
        
        <div class="field">
            <span class="label">Address:</span>
            <span class="value">' . ($firstEmployer->address ?? 'Jakarta, Indonesia') . '</span>
        </div>
        
        <div class="field">
            <span class="label">Status:</span>
            <span class="value">Active</span>
        </div>
    </div>
    
    <div class="footer">
        <p>This is a sample document for testing purposes.</p>
        <p>Generated on ' . now()->format('d F Y H:i:s') . '</p>
    </div>
</body>
</html>';

            $fileName = 'business_registration_' . $firstEmployer->id . '.html';
            $filePath = $uploadsPath . '/' . $fileName;

            Storage::disk('public')->put($filePath, $htmlContent);

            $document = VerificationDocument::create([
                'employer_id' => $firstEmployer->id,
                'document_type' => 'business_registration',
                'file_path' => $filePath,
                'status' => VerificationDocument::STATUS_SUBMITTED,
                'submission_date' => now()->subDays(5),
                'notes' => 'Sample business registration document',
            ]);

            VerificationDocumentHistory::create([
                'verification_document_id' => $document->id,
                'file_path' => $filePath,
                'status' => VerificationDocument::STATUS_SUBMITTED,
                'notes' => 'Business registration certificate uploaded',
                'upload_at' => now()->subDays(5),
            ]);
        }

        $this->command->info('Test documents created successfully!');
        $this->command->info('Created documents for ' . $employers->count() . ' employers');
        $this->command->info('Files stored in: storage/app/public/' . $uploadsPath);
    }
}
