<div class="winforms-main-content">
    <style>
        /* Additional WinForms-specific styles for CRUD interface */
        .winforms-groupbox {
            border: 2px inset #c0c0c0;
            background: #ece9d8;
            padding: 12px;
            margin-bottom: 12px;
            position: relative;
        }

        .winforms-groupbox-title {
            position: absolute;
            top: -8px;
            left: 8px;
            background: #ece9d8;
            padding: 0 4px;
            font-weight: bold;
            font-size: 11px;
            color: #333;
        }

        .winforms-textbox {
            border: 2px inset #c0c0c0;
            background: white;
            padding: 2px 4px;
            font-size: 11px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            width: 100%;
            box-sizing: border-box;
        }

        .winforms-textbox:focus {
            outline: none;
            border: 2px inset #0078d4;
        }

        .winforms-textbox:disabled {
            background: #f0f0f0;
            color: #666;
        }

        .winforms-checkbox {
            margin-right: 6px;
        }

        .winforms-datagrid {
            border: 2px inset #c0c0c0;
            background: white;
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .winforms-datagrid th {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 4px 6px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
            color: #333;
        }

        .winforms-datagrid td {
            border: 1px solid #e0e0e0;
            padding: 4px 6px;
            background: white;
        }

        .winforms-datagrid tr:nth-child(even) td {
            background: #f8f8f8;
        }

        .winforms-datagrid tr:hover td {
            background: #e6f3ff;
        }

        .winforms-button-group {
            display: flex;
            gap: 6px;
            margin-bottom: 12px;
        }

        .winforms-button-primary {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px outset #0078d4;
            color: white;
            padding: 4px 12px;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
        }

        .winforms-button-primary:hover {
            background: linear-gradient(to bottom, #5aa6ff, #106ebe);
        }

        .winforms-button-primary:active {
            border: 1px inset #0078d4;
            background: linear-gradient(to bottom, #0078d4, #4a9eff);
        }

        .winforms-button-danger {
            background: linear-gradient(to bottom, #ff6b6b, #e74c3c);
            border: 1px outset #e74c3c;
            color: white;
            padding: 2px 8px;
            cursor: pointer;
            font-size: 10px;
        }

        .winforms-button-danger:hover {
            background: linear-gradient(to bottom, #ff7979, #c0392b);
        }

        .winforms-button-success {
            background: linear-gradient(to bottom, #2ecc71, #27ae60);
            border: 1px outset #27ae60;
            color: white;
            padding: 2px 8px;
            cursor: pointer;
            font-size: 10px;
        }

        .winforms-button-success:hover {
            background: linear-gradient(to bottom, #58d68d, #229954);
        }

        .winforms-form-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 8px;
        }

        .winforms-form-label {
            width: 120px;
            font-weight: bold;
            font-size: 11px;
            color: #333;
            text-align: right;
        }

        .winforms-form-input {
            flex: 1;
        }

        .winforms-error {
            color: #e74c3c;
            font-size: 10px;
            margin-top: 2px;
        }

        .winforms-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 8px;
            margin-bottom: 12px;
            font-size: 11px;
        }

        .winforms-error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 8px;
            margin-bottom: 12px;
            font-size: 11px;
        }

        .winforms-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .winforms-modal-content {
            background: #ece9d8;
            border: 2px outset #c0c0c0;
            box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
            padding: 16px;
            min-width: 400px;
            max-width: 600px;
        }

        .winforms-modal-header {
            background: linear-gradient(to bottom, #d4d0c8, #c0c0c0);
            border-bottom: 1px solid #c0c0c0;
            padding: 4px 8px;
            margin: -16px -16px 12px -16px;
            font-weight: bold;
            font-size: 12px;
            color: #333;
        }

        .winforms-search-box {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
        }

        .winforms-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
            margin-top: 12px;
            font-size: 11px;
        }

        .winforms-pagination button {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 10px;
        }

        .winforms-pagination button:hover {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
        }

        .winforms-pagination button:disabled {
            background: #f0f0f0;
            color: #999;
            cursor: not-allowed;
        }

        .winforms-pagination .active {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            color: white;
            border: 1px inset #0078d4;
        }

        /* Master-Detail Interface Styles */
        .winforms-expandable-row {
            cursor: pointer;
        }

        .winforms-expandable-row:hover {
            background: #f0f8ff !important;
        }

        .winforms-detail-row {
            background: #f8f8f8 !important;
        }

        .winforms-detail-content {
            padding: 12px;
            border: 1px inset #c0c0c0;
            background: #f0f0f0;
            margin: 4px;
        }

        .winforms-answer-item {
            background: white;
            border: 1px inset #c0c0c0;
            padding: 6px 8px;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
        }

        .winforms-answer-text {
            flex: 1;
            margin-right: 8px;
        }

        .winforms-answer-actions {
            display: flex;
            gap: 2px;
        }

        .winforms-answer-form {
            background: #ece9d8;
            border: 1px inset #c0c0c0;
            padding: 8px;
            margin-bottom: 8px;
        }

        .winforms-answer-input {
            border: 2px inset #c0c0c0;
            background: white;
            padding: 4px;
            font-size: 11px;
            width: 100%;
            margin-bottom: 6px;
            box-sizing: border-box;
        }

        .winforms-expand-icon {
            margin-right: 6px;
            font-size: 10px;
            color: #666;
            transition: transform 0.2s ease;
        }

        .winforms-expand-icon.expanded {
            transform: rotate(90deg);
        }

        .winforms-answer-count {
            background: #e6f3ff;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            color: #0078d4;
            font-weight: bold;
        }

        .winforms-no-answers {
            color: #999;
            font-style: italic;
            font-size: 10px;
        }

        .winforms-button-small {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            color: #333;
            padding: 1px 4px;
            cursor: pointer;
            font-size: 9px;
            margin: 0 1px;
        }

        .winforms-button-small:hover {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
        }

        .winforms-button-small:active {
            border: 1px inset #c0c0c0;
        }

        .winforms-button-small.success {
            background: linear-gradient(to bottom, #2ecc71, #27ae60);
            color: white;
            border: 1px outset #27ae60;
        }

        .winforms-button-small.danger {
            background: linear-gradient(to bottom, #ff6b6b, #e74c3c);
            color: white;
            border: 1px outset #e74c3c;
        }

        .winforms-inline-edit {
            background: #fff8dc;
            border: 1px solid #ddd;
            padding: 4px;
            margin: 2px 0;
        }

        /* Edit Form Flash Effect */
        .winforms-form-flash {
            animation: winforms-edit-flash 0.8s ease-in-out;
        }

        @keyframes winforms-edit-flash {
            0% {
                border: 2px inset #c0c0c0;
                background: #ece9d8;
                box-shadow: none;
            }
            15% {
                border: 2px solid #4a9eff;
                background: #f0f8ff;
                box-shadow: 0 0 8px rgba(74, 158, 255, 0.4), inset 0 0 8px rgba(74, 158, 255, 0.1);
            }
            30% {
                border: 2px solid #0078d4;
                background: #e6f3ff;
                box-shadow: 0 0 12px rgba(0, 120, 212, 0.5), inset 0 0 12px rgba(0, 120, 212, 0.15);
            }
            45% {
                border: 2px solid #4a9eff;
                background: #f0f8ff;
                box-shadow: 0 0 8px rgba(74, 158, 255, 0.4), inset 0 0 8px rgba(74, 158, 255, 0.1);
            }
            60% {
                border: 2px solid #87ceeb;
                background: #f8fcff;
                box-shadow: 0 0 6px rgba(135, 206, 235, 0.3), inset 0 0 6px rgba(135, 206, 235, 0.1);
            }
            100% {
                border: 2px inset #c0c0c0;
                background: #ece9d8;
                box-shadow: none;
            }
        }

        /* Subtle pulse effect for form title during flash */
        .winforms-form-title-flash {
            animation: winforms-title-pulse 0.8s ease-in-out;
        }

        @keyframes winforms-title-pulse {
            0%, 100% {
                color: #333;
                text-shadow: none;
            }
            30% {
                color: #0078d4;
                text-shadow: 0 0 2px rgba(0, 120, 212, 0.3);
            }
        }
    </style>

    <!-- Page Header -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Pertanyaan Preferensi Management</div>

        <!-- Success/Error Messages -->
        @if (session()->has('message'))
            <div class="winforms-success">
                <i class="fas fa-check-circle" style="margin-right: 6px;"></i>
                {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="winforms-error-message">
                <i class="fas fa-exclamation-triangle" style="margin-right: 6px;"></i>
                {{ session('error') }}
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="winforms-button-group">
            <button class="winforms-button-primary" wire:click="showCreateForm">
                <i class="fas fa-plus" style="margin-right: 4px; font-size: 10px;"></i>
                Add New Pertanyaan
            </button>

            @if($showForm)
                <button class="winforms-button" wire:click="cancel">
                    <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                    Cancel
                </button>
            @endif
        </div>

        <!-- Search Box -->
        <div class="winforms-search-box">
            <label class="winforms-form-label" style="width: auto;">Search:</label>
            <input type="text" class="winforms-textbox" style="width: 300px;"
                   wire:model.live.debounce.300ms="search"
                   placeholder="Search pertanyaan...">
            @if($search)
                <button class="winforms-button" wire:click="$set('search', '')" title="Clear search">
                    <i class="fas fa-times" style="font-size: 10px;"></i>
                </button>
            @endif
        </div>
    </div>

    <!-- Form Section -->
    @if($showForm)
        <div class="winforms-groupbox {{ $editMode ? 'winforms-form-flash' : '' }}"
             wire:key="form-{{ $editMode ? 'edit-' . $pertanyaan_id : 'create' }}">
            <div class="winforms-groupbox-title {{ $editMode ? 'winforms-form-title-flash' : '' }}">
                {{ $editMode ? 'Edit Pertanyaan' : 'Add New Pertanyaan' }}
            </div>

            <form wire:submit.prevent="save">
                <div class="winforms-form-row">
                    <label class="winforms-form-label">Teks Pertanyaan:</label>
                    <div class="winforms-form-input">
                        <textarea class="winforms-textbox" wire:model="teks_pertanyaan"
                                  placeholder="Enter question text" rows="3" maxlength="500"></textarea>
                        @error('teks_pertanyaan')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row">
                    <label class="winforms-form-label">Tipe Pertanyaan:</label>
                    <div class="winforms-form-input">
                        <select class="winforms-textbox" wire:model="tipe_pertanyaan">
                            <option value="">Select question type</option>
                            <option value="multiple_choice">Multiple Choice</option>
                            <option value="single_choice">Single Choice</option>
                            <option value="text">Text Input</option>
                            <option value="rating">Rating Scale</option>
                            <option value="boolean">Yes/No</option>
                        </select>
                        @error('tipe_pertanyaan')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row">
                    <label class="winforms-form-label">Kategori:</label>
                    <div class="winforms-form-input">
                        <select class="winforms-textbox" wire:model="kategori">
                            <option value="">Select category</option>
                            <option value="personal">Personal</option>
                            <option value="professional">Professional</option>
                            <option value="education">Education</option>
                            <option value="lifestyle">Lifestyle</option>
                            <option value="preferences">Preferences</option>
                            <option value="skills">Skills</option>
                        </select>
                        @error('kategori')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row">
                    <label class="winforms-form-label">Urutan:</label>
                    <div class="winforms-form-input">
                        <input type="number" class="winforms-textbox" wire:model="urutan"
                               placeholder="Enter order number" min="1" style="width: 100px;">
                        @error('urutan')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row">
                    <label class="winforms-form-label">Status:</label>
                    <div class="winforms-form-input">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" class="winforms-checkbox" wire:model="status">
                            Active
                        </label>
                        @error('status')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row" style="justify-content: flex-end; margin-top: 16px;">
                    <div style="display: flex; gap: 6px;">
                        <button type="submit" class="winforms-button-primary">
                            <i class="fas fa-save" style="margin-right: 4px; font-size: 10px;"></i>
                            {{ $editMode ? 'Update' : 'Save' }}
                        </button>
                        <button type="button" class="winforms-button" wire:click="cancel">
                            <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    @endif

    <!-- Data Grid Section -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Pertanyaan List</div>

        @if($pertanyaan->count() > 0)
            <table class="winforms-datagrid">
                <thead>
                    <tr>
                        <th style="width: 30px;"></th>
                        <th style="width: 50px;">ID</th>
                        <th>Teks Pertanyaan</th>
                        <th style="width: 120px;">Tipe</th>
                        <th style="width: 100px;">Kategori</th>
                        <th style="width: 60px;">Urutan</th>
                        <th style="width: 80px;">Status</th>
                        <th style="width: 80px;">Answers</th>
                        <th style="width: 120px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($pertanyaan as $item)
                        <!-- Main Question Row -->
                        <tr class="winforms-expandable-row" wire:click="toggleRow({{ $item->id }})">
                            <td style="text-align: center;">
                                <i class="fas fa-chevron-right winforms-expand-icon {{ in_array($item->id, $expandedRows) ? 'expanded' : '' }}"></i>
                            </td>
                            <td>{{ $item->id }}</td>
                            <td>
                                <strong>{{ Str::limit($item->teks_pertanyaan, 50) }}</strong>
                            </td>
                            <td>
                                <span style="background: #e6f3ff; padding: 2px 4px; border-radius: 2px; font-size: 10px;">
                                    {{ ucfirst(str_replace('_', ' ', $item->tipe_pertanyaan)) }}
                                </span>
                            </td>
                            <td>
                                <span style="background: #f0f8ff; padding: 2px 4px; border-radius: 2px; font-size: 10px;">
                                    {{ ucfirst($item->kategori) }}
                                </span>
                            </td>
                            <td style="text-align: center;">
                                <strong>{{ $item->urutan }}</strong>
                            </td>
                            <td>
                                @if($item->status)
                                    <span style="color: #27ae60; font-weight: bold;">
                                        <i class="fas fa-check-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                        Active
                                    </span>
                                @else
                                    <span style="color: #e74c3c; font-weight: bold;">
                                        <i class="fas fa-times-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                        Inactive
                                    </span>
                                @endif
                            </td>
                            <td style="text-align: center;">
                                @if($item->jawaban->count() > 0)
                                    <span class="winforms-answer-count">{{ $item->jawaban->count() }}</span>
                                @else
                                    <span class="winforms-no-answers">No answers</span>
                                @endif
                            </td>
                            <td onclick="event.stopPropagation();">
                                <div style="display: flex; gap: 2px;">
                                    <button class="winforms-button-success"
                                            wire:click="showEditForm({{ $item->id }})"
                                            title="Edit pertanyaan">
                                        <i class="fas fa-edit" style="font-size: 9px;"></i>
                                    </button>
                                    <button class="winforms-button-danger"
                                            wire:click="confirmDelete({{ $item->id }})"
                                            title="Delete pertanyaan">
                                        <i class="fas fa-trash" style="font-size: 9px;"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <!-- Expandable Detail Row for Answers -->
                        @if(in_array($item->id, $expandedRows))
                            <tr class="winforms-detail-row">
                                <td colspan="9">
                                    <div class="winforms-detail-content">
                                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
                                            <h4 style="margin: 0; font-size: 12px; color: #333;">
                                                <i class="fas fa-list" style="margin-right: 6px;"></i>
                                                Answers for: {{ Str::limit($item->teks_pertanyaan, 80) }}
                                            </h4>
                                            <button class="winforms-button-primary"
                                                    wire:click="showAddAnswerForm({{ $item->id }})"
                                                    style="font-size: 10px; padding: 2px 6px; margin-left:12px">
                                                <i class="fas fa-plus" style="margin-right: 4px; font-size: 9px;"></i>
                                                Add Answer
                                            </button>
                                        </div>

                                        <!-- Add New Answer Form -->
                                        @if(isset($showAnswerForm[$item->id]) && $showAnswerForm[$item->id])
                                            <div class="winforms-answer-form">
                                                <div style="font-weight: bold; margin-bottom: 4px; font-size: 11px;">Add New Answer:</div>
                                                <input type="text"
                                                       class="winforms-answer-input"
                                                       wire:model="newAnswerText.{{ $item->id }}"
                                                       placeholder="Enter answer text..."
                                                       maxlength="500">
                                                @error("newAnswerText.{$item->id}")
                                                    <div class="winforms-error">{{ $message }}</div>
                                                @enderror
                                                <div style="display: flex; gap: 4px; margin-top: 4px;">
                                                    <button class="winforms-button-small success"
                                                            wire:click="saveNewAnswer({{ $item->id }})">
                                                        <i class="fas fa-save" style="margin-right: 2px;"></i>
                                                        Save
                                                    </button>
                                                    <button class="winforms-button-small"
                                                            wire:click="hideAddAnswerForm({{ $item->id }})">
                                                        <i class="fas fa-times" style="margin-right: 2px;"></i>
                                                        Cancel
                                                    </button>
                                                </div>
                                            </div>
                                        @endif

                                        <!-- Existing Answers List -->
                                        @if($item->jawaban->count() > 0)
                                            @foreach($item->jawaban as $answer)
                                                <div class="winforms-answer-item">
                                                    @if(isset($editingAnswer[$answer->id]) && $editingAnswer[$answer->id])
                                                        <!-- Edit Mode -->
                                                        <div class="winforms-inline-edit" style="flex: 1;">
                                                            <input type="text"
                                                                   class="winforms-answer-input"
                                                                   wire:model="editAnswerText.{{ $answer->id }}"
                                                                   style="margin-bottom: 4px;">
                                                            @error("editAnswerText.{$answer->id}")
                                                                <div class="winforms-error">{{ $message }}</div>
                                                            @enderror
                                                            <div style="display: flex; gap: 4px;">
                                                                <button class="winforms-button-small success"
                                                                        wire:click="updateAnswer({{ $answer->id }})">
                                                                    <i class="fas fa-save" style="margin-right: 2px;"></i>
                                                                    Save
                                                                </button>
                                                                <button class="winforms-button-small"
                                                                        wire:click="cancelEditAnswer({{ $answer->id }})">
                                                                    <i class="fas fa-times" style="margin-right: 2px;"></i>
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        </div>
                                                    @else
                                                        <!-- View Mode -->
                                                        <div class="winforms-answer-text">
                                                            {{ $answer->teks_jawaban }}
                                                        </div>
                                                        <div class="winforms-answer-actions">
                                                            <button class="winforms-button-small"
                                                                    wire:click="editAnswer({{ $answer->id }})"
                                                                    title="Edit answer">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="winforms-button-small danger"
                                                                    wire:click="confirmDeleteAnswer({{ $answer->id }})"
                                                                    title="Delete answer">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endforeach
                                        @else
                                            <div style="text-align: center; padding: 16px; color: #666; font-style: italic;">
                                                <i class="fas fa-info-circle" style="margin-right: 6px;"></i>
                                                No answers available for this question.
                                            </div>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="winforms-pagination">
                {{ $pertanyaan->links() }}
            </div>
        @else
            <div style="text-align: center; padding: 32px; color: #666;">
                <i class="fas fa-inbox" style="font-size: 32px; margin-bottom: 8px; display: block;"></i>
                @if($search)
                    <p>No pertanyaan found matching "{{ $search }}"</p>
                    <button class="winforms-button" wire:click="$set('search', '')">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Clear Search
                    </button>
                @else
                    <p>No pertanyaan available</p>
                    <button class="winforms-button-primary" wire:click="showCreateForm">
                        <i class="fas fa-plus" style="margin-right: 4px; font-size: 10px;"></i>
                        Add First Pertanyaan
                    </button>
                @endif
            </div>
        @endif
    </div>

    <!-- Delete Confirmation Modal -->
    @if($confirmingDeletion && $pertanyaanToDelete)
        <div class="winforms-modal">
            <div class="winforms-modal-content">
                <div class="winforms-modal-header">
                    <i class="fas fa-exclamation-triangle" style="margin-right: 6px; color: #e74c3c;"></i>
                    Confirm Deletion
                </div>

                <div style="padding: 8px 0;">
                    <p style="margin: 0 0 12px 0; font-size: 11px; line-height: 1.4;">
                        Are you sure you want to delete the following pertanyaan?
                    </p>

                    <div style="background: #f8f8f8; border: 1px inset #c0c0c0; padding: 8px; margin: 8px 0;">
                        <div style="font-weight: bold; margin-bottom: 4px;">
                            {{ Str::limit($pertanyaanToDelete->teks_pertanyaan, 100) }}
                        </div>
                        <div style="color: #666; font-size: 10px;">
                            Type: {{ ucfirst(str_replace('_', ' ', $pertanyaanToDelete->tipe_pertanyaan)) }} |
                            Category: {{ ucfirst($pertanyaanToDelete->kategori) }} |
                            Order: {{ $pertanyaanToDelete->urutan }}
                        </div>
                        @if($pertanyaanToDelete->jawaban->count() > 0)
                            <div style="color: #e74c3c; font-size: 10px; margin-top: 4px;">
                                <i class="fas fa-warning" style="margin-right: 4px;"></i>
                                This will also delete {{ $pertanyaanToDelete->jawaban->count() }} related answer(s).
                            </div>
                        @endif
                    </div>

                    <p style="margin: 12px 0 0 0; font-size: 10px; color: #e74c3c;">
                        <i class="fas fa-warning" style="margin-right: 4px;"></i>
                        This action cannot be undone.
                    </p>
                </div>

                <div style="display: flex; justify-content: flex-end; gap: 6px; margin-top: 16px;">
                    <button class="winforms-button-danger" wire:click="delete">
                        <i class="fas fa-trash" style="margin-right: 4px; font-size: 10px;"></i>
                        Delete
                    </button>
                    <button class="winforms-button" wire:click="cancelDelete">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Answer Delete Confirmation Modal -->
    @if($confirmingAnswerDeletion && $answerToDelete)
        <div class="winforms-modal">
            <div class="winforms-modal-content">
                <div class="winforms-modal-header">
                    <i class="fas fa-exclamation-triangle" style="margin-right: 6px; color: #e74c3c;"></i>
                    Confirm Answer Deletion
                </div>

                <div style="padding: 8px 0;">
                    <p style="margin: 0 0 12px 0; font-size: 11px; line-height: 1.4;">
                        Are you sure you want to delete this answer?
                    </p>

                    <div style="background: #f8f8f8; border: 1px inset #c0c0c0; padding: 8px; margin: 8px 0;">
                        <div style="font-weight: bold; margin-bottom: 4px; font-size: 11px;">
                            Answer Text:
                        </div>
                        <div style="color: #333; font-size: 11px; line-height: 1.3;">
                            "{{ $answerToDelete->teks_jawaban }}"
                        </div>
                        <div style="color: #666; font-size: 10px; margin-top: 4px;">
                            For question: {{ Str::limit($answerToDelete->pertanyaan->teks_pertanyaan, 60) }}
                        </div>
                    </div>

                    <p style="margin: 12px 0 0 0; font-size: 10px; color: #e74c3c;">
                        <i class="fas fa-warning" style="margin-right: 4px;"></i>
                        This action cannot be undone.
                    </p>
                </div>

                <div style="display: flex; justify-content: flex-end; gap: 6px; margin-top: 16px;">
                    <button class="winforms-button-danger" wire:click="deleteAnswer">
                        <i class="fas fa-trash" style="margin-right: 4px; font-size: 10px;"></i>
                        Delete Answer
                    </button>
                    <button class="winforms-button" wire:click="cancelDeleteAnswer">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Custom Pagination Styles -->
    <style>
        .winforms-pagination nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2px;
        }

        .winforms-pagination .pagination {
            display: flex;
            gap: 2px;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .winforms-pagination .page-item {
            display: block;
        }

        .winforms-pagination .page-link {
            display: block;
            padding: 2px 6px;
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            color: #333;
            text-decoration: none;
            font-size: 10px;
            cursor: pointer;
        }

        .winforms-pagination .page-link:hover {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
            color: #333;
        }

        .winforms-pagination .page-item.active .page-link {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px inset #0078d4;
            color: white;
        }

        .winforms-pagination .page-item.disabled .page-link {
            background: #f0f0f0;
            color: #999;
            cursor: not-allowed;
        }

        .winforms-pagination .page-item.disabled .page-link:hover {
            background: #f0f0f0;
            color: #999;
        }
    </style>

    <script>
        // Flash effect management
        document.addEventListener('livewire:init', () => {
            Livewire.on('reset-flash-effect', () => {
                // Reset flash effect after animation duration
                setTimeout(() => {
                    @this.set('flashEffect', false);
                }, 800);
            });
        });

        // Enhanced visual feedback for edit mode
        document.addEventListener('DOMContentLoaded', function() {
            // Add subtle sound effect simulation (visual feedback)
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const target = mutation.target;
                        if (target.classList.contains('winforms-form-flash')) {
                            // Add a subtle vibration effect to the form inputs during flash
                            const inputs = target.querySelectorAll('.winforms-textbox');
                            inputs.forEach(input => {
                                input.style.transition = 'all 0.1s ease-in-out';
                                setTimeout(() => {
                                    input.style.transform = 'translateX(1px)';
                                    setTimeout(() => {
                                        input.style.transform = 'translateX(-1px)';
                                        setTimeout(() => {
                                            input.style.transform = 'translateX(0)';
                                            input.style.transition = '';
                                        }, 50);
                                    }, 50);
                                }, 200);
                            });
                        }
                    }
                });
            });

            // Observe the form container for class changes
            const formContainer = document.querySelector('.winforms-groupbox');
            if (formContainer) {
                observer.observe(formContainer, { attributes: true, subtree: true });
            }
        });
    </script>
</div>
