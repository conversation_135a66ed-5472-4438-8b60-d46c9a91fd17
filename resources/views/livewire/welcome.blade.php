<div class="winforms-main-content">
    <h2 style="margin: 0 0 16px 0; font-size: 14px; color: #333; font-weight: bold;">Welcome to Admin Panel</h2>
    <p style="margin: 0 0 12px 0; color: #666; line-height: 1.4;">
        This is the main content area with authentic Windows Forms styling. Select an item from the navigation tree on the left to begin working with different modules.
    </p>
    <p style="margin: 0 0 12px 0; color: #666; line-height: 1.4;">
        The layout features classic WinForms elements including:
    </p>
    <ul style="margin: 0 0 16px 20px; color: #666; line-height: 1.4;">
        <li>Authentic #ece9d8 beige background color</li>
        <li>Proper inset/outset 2px groove borders</li>
        <li>Classic 3D button effects with hover states</li>
        <li>Traditional Windows tree navigation</li>
        <li>11px system font sizing</li>
        <li>Desktop application behavior and feel</li>
    </ul>

    <div style="margin-top: 24px; padding: 12px; border: 2px inset #c0c0c0; background: #f0f0f0;">
        <h3 style="margin: 0 0 8px 0; font-size: 12px; font-weight: bold; color: #333;">Quick Actions</h3>
        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
            <button class="winforms-button" onclick="showMessage('New Item')">
                <i class="fas fa-plus" style="margin-right: 4px; font-size: 10px;"></i>
                New Item
            </button>
            <button class="winforms-button" onclick="showMessage('Edit Item')">
                <i class="fas fa-edit" style="margin-right: 4px; font-size: 10px;"></i>
                Edit
            </button>
            <button class="winforms-button" onclick="showMessage('Delete Item')">
                <i class="fas fa-trash" style="margin-right: 4px; font-size: 10px;"></i>
                Delete
            </button>
            <button class="winforms-button" onclick="showMessage('Refresh Data')">
                <i class="fas fa-sync" style="margin-right: 4px; font-size: 10px;"></i>
                Refresh
            </button>
            <button class="winforms-button" onclick="showMessage('Print Report')">
                <i class="fas fa-print" style="margin-right: 4px; font-size: 10px;"></i>
                Print
            </button>
        </div>
    </div>

    <div style="margin-top: 16px; padding: 8px; border: 2px inset #c0c0c0; background: white; font-family: 'Courier New', monospace; font-size: 10px; color: #333;">
        <div style="font-weight: bold; margin-bottom: 4px;">System Status:</div>
        <div>Application: Ready</div>
        <div>Database: Connected</div>
        <div>User: Administrator</div>
        <div>Last Login: <span id="current-time"></span></div>
    </div>
</div>