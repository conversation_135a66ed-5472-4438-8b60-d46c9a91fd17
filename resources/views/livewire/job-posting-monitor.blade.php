<div class="winforms-main-content">
    <!-- <PERSON> Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h2 style="margin: 0; font-size: 16px; color: #333; font-weight: bold;">
            <i class="fas fa-briefcase" style="margin-right: 6px; color: #0078d4;"></i>
            Job Posting Monitor
        </h2>
        <div style="font-size: 10px; color: #666;">
            <i class="fas fa-clock" style="margin-right: 4px;"></i>
            Last Updated: {{ now()->format('Y-m-d H:i:s') }}
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="winforms-success">
            <i class="fas fa-check-circle" style="margin-right: 6px;"></i>
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="winforms-error-message">
            <i class="fas fa-exclamation-triangle" style="margin-right: 6px;"></i>
            {{ session('error') }}
        </div>
    @endif

    <!-- Search and Filter Controls -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Search & Filter</div>
        
        <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap; margin-bottom: 12px;">
            <!-- Search Box -->
            <div class="winforms-search-box" style="flex: 1; min-width: 200px;">
                <label style="font-weight: bold; font-size: 11px; color: #333; margin-right: 6px;">Search:</label>
                <input type="text" 
                       wire:model.live.debounce.300ms="search" 
                       class="winforms-textbox" 
                       placeholder="Job title, company, or location..."
                       style="flex: 1;">
                <i class="fas fa-search" style="margin-left: 6px; color: #666; font-size: 10px;"></i>
            </div>

            <!-- Status Filter -->
            <div style="display: flex; align-items: center; gap: 6px;">
                <label style="font-weight: bold; font-size: 11px; color: #333;">Status:</label>
                <select wire:model.live="statusFilter" class="winforms-textbox" style="width: 120px;">
                    @foreach($statusOptions as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
            <!-- Job Type Filter -->
            <div style="display: flex; align-items: center; gap: 6px;">
                <label style="font-weight: bold; font-size: 11px; color: #333;">Type:</label>
                <select wire:model.live="jobTypeFilter" class="winforms-textbox" style="width: 120px;">
                    @foreach($jobTypeOptions as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Location Filter -->
            <div style="display: flex; align-items: center; gap: 6px;">
                <label style="font-weight: bold; font-size: 11px; color: #333;">Location:</label>
                <input type="text" 
                       wire:model.live.debounce.300ms="locationFilter" 
                       class="winforms-textbox" 
                       placeholder="Filter by location..."
                       style="width: 150px;">
            </div>
        </div>
    </div>

    <!-- Statistics Summary -->
    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #0078d4;">{{ $jobPostings->total() }}</div>
            <div style="font-size: 10px; color: #666;">Total Jobs</div>
        </div>
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #27ae60;">
                {{ \App\Models\JobPosting::where('is_active', true)->count() }}
            </div>
            <div style="font-size: 10px; color: #666;">Active</div>
        </div>
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #e74c3c;">
                {{ \App\Models\JobPosting::where('is_active', false)->count() }}
            </div>
            <div style="font-size: 10px; color: #666;">Inactive</div>
        </div>
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #f39c12;">
                {{ \App\Models\JobPosting::where('application_deadline', '<', now())->count() }}
            </div>
            <div style="font-size: 10px; color: #666;">Expired</div>
        </div>
    </div>

    <!-- Job Postings Data Grid -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Job Postings</div>
        
        @if($jobPostings->count() > 0)
            <table class="winforms-datagrid">
                <thead>
                    <tr>
                        <th style="width: 50px;">ID</th>
                        <th>Job Information</th>
                        <th style="width: 120px;">Company</th>
                        <th style="width: 100px;">Location</th>
                        <th style="width: 80px;">Type</th>
                        <th style="width: 80px;">Status</th>
                        <th style="width: 80px;">Views</th>
                        <th style="width: 80px;">Applications</th>
                        <th style="width: 100px;">Posted Date</th>
                        <th style="width: 120px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($jobPostings as $job)
                        <tr class="{{ $flashEffect ? 'winforms-form-flash' : '' }}">
                            <td>{{ $job->id }}</td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 2px;">
                                    <strong>{{ Str::limit($job->title, 30) }}</strong>
                                    <div style="font-size: 10px; color: #666;">
                                        {{ Str::limit($job->description, 50) }}
                                    </div>
                                    @if($job->application_deadline)
                                        <div style="font-size: 10px; color: {{ $job->application_deadline < now() ? '#e74c3c' : '#f39c12' }};">
                                            <i class="fas fa-calendar" style="margin-right: 2px;"></i>
                                            Deadline: {{ $job->application_deadline->format('Y-m-d') }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 11px;">
                                    {{ Str::limit($job->employer->company_name ?? 'N/A', 15) }}
                                </div>
                                @if($job->employer && $job->employer->is_verified)
                                    <div style="font-size: 9px; color: #27ae60;">
                                        <i class="fas fa-check-circle" style="margin-right: 2px;"></i>
                                        Verified
                                    </div>
                                @endif
                            </td>
                            <td style="font-size: 10px;">
                                {{ Str::limit($job->location, 15) ?? '-' }}
                                @if($job->is_remote)
                                    <div style="color: #0078d4; font-size: 9px;">
                                        <i class="fas fa-home" style="margin-right: 2px;"></i>
                                        Remote
                                    </div>
                                @endif
                            </td>
                            <td style="font-size: 10px;">
                                {{ ucfirst(str_replace('-', ' ', $job->job_type ?? 'N/A')) }}
                            </td>
                            <td>
                                @if($job->is_active)
                                    @if($job->application_deadline && $job->application_deadline < now())
                                        <span style="color: #f39c12; font-weight: bold;">
                                            <i class="fas fa-clock" style="font-size: 10px; margin-right: 2px;"></i>
                                            Expired
                                        </span>
                                    @else
                                        <span style="color: #27ae60; font-weight: bold;">
                                            <i class="fas fa-check-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                            Active
                                        </span>
                                    @endif
                                @else
                                    <span style="color: #e74c3c; font-weight: bold;">
                                        <i class="fas fa-times-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                        Inactive
                                    </span>
                                @endif
                            </td>
                            <td style="text-align: center;">
                                <span style="background: #e8e8e8; padding: 2px 6px; border-radius: 2px; font-size: 10px;">
                                    {{ number_format($job->views_count ?? 0) }}
                                </span>
                            </td>
                            <td style="text-align: center;">
                                <span style="background: #e8e8e8; padding: 2px 6px; border-radius: 2px; font-size: 10px;">
                                    {{ number_format($job->applicants_count ?? 0) }}
                                </span>
                            </td>
                            <td style="font-size: 10px;">
                                {{ $job->created_at ? $job->created_at->format('Y-m-d') : 'N/A' }}
                            </td>
                            <td>
                                <div style="display: flex; gap: 2px; flex-wrap: wrap;">
                                    <button class="winforms-button" 
                                            wire:click="showJobDetails({{ $job->id }})"
                                            title="View Details">
                                        <i class="fas fa-eye" style="font-size: 9px;"></i>
                                    </button>
                                    
                                    @if($job->is_active)
                                        <button class="winforms-button-danger" 
                                                wire:click="deactivateJob({{ $job->id }})"
                                                title="Deactivate Job"
                                                onclick="return confirm('Are you sure you want to deactivate this job posting?')">
                                            <i class="fas fa-pause" style="font-size: 9px;"></i>
                                        </button>
                                    @else
                                        <button class="winforms-button-success" 
                                                wire:click="activateJob({{ $job->id }})"
                                                title="Activate Job"
                                                onclick="return confirm('Are you sure you want to activate this job posting?')">
                                            <i class="fas fa-play" style="font-size: 9px;"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div style="text-align: center; padding: 32px; color: #666;">
                <i class="fas fa-search" style="font-size: 32px; margin-bottom: 8px; opacity: 0.5;"></i>
                <div style="font-size: 12px;">No job postings found matching your criteria.</div>
            </div>
        @endif
    </div>

    <!-- Custom WinForms Pagination -->
    @if($jobPostings->hasPages())
        <div class="winforms-custom-pagination">
            <div class="winforms-pagination-container">
                <div class="winforms-page-info">
                    <span>Page {{ $jobPostings->currentPage() }} of {{ $jobPostings->lastPage() }}</span>
                    <span class="winforms-total-records">
                        ({{ $jobPostings->firstItem() }}-{{ $jobPostings->lastItem() }} of {{ $jobPostings->total() }} jobs)
                    </span>
                </div>

                <div class="winforms-pagination-controls">
                    <button class="winforms-pagination-btn"
                            wire:click="goToPage(1)"
                            @if($jobPostings->currentPage() == 1) disabled @endif
                            title="First Page">
                        <i class="fas fa-angle-double-left"></i>
                    </button>

                    <button class="winforms-pagination-btn"
                            wire:click="goPreviousPage"
                            @if(!$jobPostings->previousPageUrl()) disabled @endif
                            title="Previous Page">
                        <i class="fas fa-angle-left"></i>
                    </button>

                    @for($i = max(1, $jobPostings->currentPage() - 2); $i <= min($jobPostings->lastPage(), $jobPostings->currentPage() + 2); $i++)
                        <button class="winforms-pagination-btn {{ $i == $jobPostings->currentPage() ? 'active' : '' }}"
                                wire:click="goToPage({{ $i }})">
                            {{ $i }}
                        </button>
                    @endfor

                    <button class="winforms-pagination-btn"
                            wire:click="goNextPage"
                            @if(!$jobPostings->nextPageUrl()) disabled @endif
                            title="Next Page">
                        <i class="fas fa-angle-right"></i>
                    </button>

                    <button class="winforms-pagination-btn"
                            wire:click="goToPage({{ $jobPostings->lastPage() }})"
                            @if($jobPostings->currentPage() == $jobPostings->lastPage()) disabled @endif
                            title="Last Page">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Job Details Modal -->
    @if($showJobModal && $selectedJob)
        <div class="winforms-modal">
            <div class="winforms-modal-content" style="max-width: 900px; width: 95%;">
                <div class="winforms-modal-header {{ $flashEffect ? 'winforms-form-title-flash' : '' }}">
                    <i class="fas fa-briefcase" style="margin-right: 6px;"></i>
                    Job Details - {{ $selectedJob->title }}
                </div>

                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 16px; margin-bottom: 16px;">
                    <!-- Left Column - Job Information -->
                    <div>
                        <div class="winforms-groupbox">
                            <div class="winforms-groupbox-title">Job Information</div>
                            <div style="font-size: 11px; line-height: 1.4;">
                                <div style="margin-bottom: 8px;">
                                    <strong>Title:</strong> {{ $selectedJob->title }}
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Company:</strong> {{ $selectedJob->employer->company_name ?? 'N/A' }}
                                    @if($selectedJob->employer && $selectedJob->employer->is_verified)
                                        <span style="color: #27ae60; font-size: 10px;">
                                            <i class="fas fa-check-circle" style="margin-left: 4px;"></i>
                                            Verified
                                        </span>
                                    @endif
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Location:</strong> {{ $selectedJob->location ?? 'Not specified' }}
                                    @if($selectedJob->is_remote)
                                        <span style="color: #0078d4; font-size: 10px;">
                                            <i class="fas fa-home" style="margin-left: 4px;"></i>
                                            Remote Work Available
                                        </span>
                                    @endif
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Job Type:</strong> {{ ucfirst(str_replace('-', ' ', $selectedJob->job_type ?? 'Not specified')) }}
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Salary:</strong> {{ $this->salaryRange }}
                                </div>
                                @if($selectedJob->application_deadline)
                                    <div style="margin-bottom: 8px;">
                                        <strong>Application Deadline:</strong>
                                        <span style="color: {{ $selectedJob->application_deadline < now() ? '#e74c3c' : '#f39c12' }};">
                                            {{ $selectedJob->application_deadline->format('Y-m-d H:i') }}
                                        </span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        @if($selectedJob->description)
                            <div class="winforms-groupbox">
                                <div class="winforms-groupbox-title">Job Description</div>
                                <div style="font-size: 11px; line-height: 1.4; max-height: 150px; overflow-y: auto;">
                                    {{ $selectedJob->description }}
                                </div>
                            </div>
                        @endif

                        @if($selectedJob->qualifications)
                            <div class="winforms-groupbox">
                                <div class="winforms-groupbox-title">Qualifications</div>
                                <div style="font-size: 11px; line-height: 1.4; max-height: 100px; overflow-y: auto;">
                                    {{ $selectedJob->qualifications }}
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Right Column - Statistics and Status -->
                    <div>
                        <div class="winforms-groupbox">
                            <div class="winforms-groupbox-title">Status & Statistics</div>
                            <div style="font-size: 11px; line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <strong>Status:</strong>
                                    <span style="color: {{ $selectedJob->is_active ? ($selectedJob->application_deadline && $selectedJob->application_deadline < now() ? '#f39c12' : '#27ae60') : '#e74c3c' }}; font-weight: bold;">
                                        {{ $this->jobStatus }}
                                    </span>
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Views:</strong> {{ number_format($selectedJob->views_count ?? 0) }}
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Applications:</strong> {{ number_format($selectedJob->applicants_count ?? 0) }}
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Posted:</strong> {{ $selectedJob->created_at ? $selectedJob->created_at->format('Y-m-d H:i') : 'N/A' }}
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>Last Updated:</strong> {{ $selectedJob->updated_at ? $selectedJob->updated_at->format('Y-m-d H:i') : 'N/A' }}
                                </div>
                            </div>
                        </div>

                        @if($selectedJob->benefits)
                            <div class="winforms-groupbox">
                                <div class="winforms-groupbox-title">Benefits</div>
                                <div style="font-size: 11px; line-height: 1.4; max-height: 100px; overflow-y: auto;">
                                    {{ $selectedJob->benefits }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Modal Footer -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px; padding-top: 12px; border-top: 1px solid #c0c0c0;">
                    <div style="display: flex; gap: 8px;">
                        @if($selectedJob->is_active)
                            <button class="winforms-button-danger"
                                    wire:click="deactivateJob({{ $selectedJob->id }})"
                                    onclick="return confirm('Are you sure you want to deactivate this job posting?')">
                                <i class="fas fa-pause" style="margin-right: 4px; font-size: 10px;"></i>
                                Deactivate Job
                            </button>
                        @else
                            <button class="winforms-button-success"
                                    wire:click="activateJob({{ $selectedJob->id }})"
                                    onclick="return confirm('Are you sure you want to activate this job posting?')">
                                <i class="fas fa-play" style="margin-right: 4px; font-size: 10px;"></i>
                                Activate Job
                            </button>
                        @endif
                    </div>

                    <button class="winforms-button" wire:click="closeJobModal">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Close
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Custom Pagination Styles -->
    <style>
        /* Custom WinForms Pagination Styles */
        .winforms-custom-pagination {
            margin-top: 12px;
            padding: 8px;
            background: #ece9d8;
            border: 1px inset #c0c0c0;
        }

        .winforms-pagination-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .winforms-page-info {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 11px;
            color: #333;
            font-weight: bold;
        }

        .winforms-total-records {
            color: #666;
            font-weight: normal;
            font-size: 10px;
        }

        .winforms-pagination-controls {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .winforms-pagination-btn {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 10px;
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.1s;
        }

        .winforms-pagination-btn:hover:not(:disabled) {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
        }

        .winforms-pagination-btn:active:not(:disabled) {
            border: 1px inset #c0c0c0;
            background: linear-gradient(to bottom, #e0e0e0, #f8f8f8);
            transform: translateY(1px);
        }

        .winforms-pagination-btn:disabled {
            background: #f0f0f0;
            color: #999;
            cursor: not-allowed;
            border: 1px solid #d0d0d0;
        }

        .winforms-pagination-btn.active {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            color: white;
            border: 1px inset #0078d4;
            font-weight: bold;
        }

        /* Flash effect for job status changes */
        .winforms-form-flash {
            animation: winforms-job-flash 0.8s ease-in-out;
        }

        @keyframes winforms-job-flash {
            0%, 100% {
                background: #ece9d8;
                border-color: #c0c0c0;
            }
            25% {
                background: #e6f3ff;
                border-color: #0078d4;
            }
            50% {
                background: #fff3cd;
                border-color: #f39c12;
            }
            75% {
                background: #e6f3ff;
                border-color: #0078d4;
            }
        }

        .winforms-form-title-flash {
            animation: winforms-title-pulse 0.8s ease-in-out;
        }

        @keyframes winforms-title-pulse {
            0%, 100% {
                color: #333;
                text-shadow: none;
            }
            30% {
                color: #0078d4;
                text-shadow: 0 0 2px rgba(0, 120, 212, 0.3);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .winforms-pagination-container {
                gap: 6px;
            }

            .winforms-pagination-controls {
                flex-wrap: wrap;
                justify-content: center;
            }

            .winforms-page-info {
                flex-direction: column;
                gap: 4px;
                text-align: center;
            }

            .winforms-modal-content {
                max-width: 95% !important;
                margin: 10px;
            }

            .winforms-modal-content > div:first-child {
                grid-template-columns: 1fr !important;
            }
        }
    </style>

    <script>
        // Flash effect management
        document.addEventListener('livewire:init', () => {
            Livewire.on('reset-flash-effect', () => {
                // Reset flash effect after animation duration
                setTimeout(() => {
                    @this.set('flashEffect', false);
                }, 800);
            });
        });
    </script>
</div>
