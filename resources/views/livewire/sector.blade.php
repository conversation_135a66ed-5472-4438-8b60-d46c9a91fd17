<div class="winforms-main-content">

    <!-- <PERSON> Header -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Sector Management</div>

        <!-- Success/Error Messages -->
        @if (session()->has('message'))
            <div class="winforms-success">
                <i class="fas fa-check-circle" style="margin-right: 6px;"></i>
                {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="winforms-error-message">
                <i class="fas fa-exclamation-triangle" style="margin-right: 6px;"></i>
                {{ session('error') }}
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="winforms-button-group">
            <button class="winforms-button-primary" wire:click="showCreateForm">
                <i class="fas fa-plus" style="margin-right: 4px; font-size: 10px;"></i>
                Add New Sector
            </button>

            @if($showForm)
                <button class="winforms-button" wire:click="cancel">
                    <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                    Cancel
                </button>
            @endif
        </div>

        <!-- Search Box -->
        <div class="winforms-search-box">
            <label class="winforms-form-label" style="width: auto;">Search:</label>
            <input type="text" class="winforms-textbox" style="width: 300px;"
                   wire:model.live.debounce.300ms="search"
                   placeholder="Search sectors...">
            @if($search)
                <button class="winforms-button" wire:click="$set('search', '')" title="Clear search">
                    <i class="fas fa-times" style="font-size: 10px;"></i>
                </button>
            @endif
        </div>
    </div>

    <!-- Form Section -->
    @if($showForm)
        <div class="winforms-groupbox {{ $editMode ? 'winforms-form-flash' : '' }}"
             wire:key="form-{{ $editMode ? 'edit-' . $sector_id : 'create' }}">
            <div class="winforms-groupbox-title {{ $editMode ? 'winforms-form-title-flash' : '' }}">
                {{ $editMode ? 'Edit Sector' : 'Add New Sector' }}
            </div>

            <form wire:submit.prevent="save">
                <div class="winforms-form-row">
                    <label class="winforms-form-label">Sector Name:</label>
                    <div class="winforms-form-input">
                        <input type="text" class="winforms-textbox" wire:model="sector"
                               placeholder="Enter sector name" maxlength="250">
                        @error('sector')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row">
                    <label class="winforms-form-label">Description:</label>
                    <div class="winforms-form-input">
                        <textarea class="winforms-textbox" wire:model="sector_desc"
                                  placeholder="Enter sector description" rows="3"></textarea>
                        @error('sector_desc')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row">
                    <label class="winforms-form-label">Status:</label>
                    <div class="winforms-form-input">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" class="winforms-checkbox" wire:model="is_active">
                            Active
                        </label>
                        @error('is_active')
                            <div class="winforms-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="winforms-form-row" style="justify-content: flex-end; margin-top: 16px;">
                    <div style="display: flex; gap: 6px;">
                        <button type="submit" class="winforms-button-primary">
                            <i class="fas fa-save" style="margin-right: 4px; font-size: 10px;"></i>
                            {{ $editMode ? 'Update' : 'Save' }}
                        </button>
                        <button type="button" class="winforms-button" wire:click="cancel">
                            <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    @endif

    <!-- Data Grid Section -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Sectors List</div>

        @if($sectors->count() > 0)
            <table class="winforms-datagrid">
                <thead>
                    <tr>
                        <th style="width: 50px;">ID</th>
                        <th>Sector Name</th>
                        <th>Description</th>
                        <th style="width: 80px;">Status</th>
                        <th style="width: 100px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($sectors as $sector)
                        <tr>
                            <td>{{ $sector->sector_id }}</td>
                            <td>
                                <strong>{{ $sector->sector }}</strong>
                            </td>
                            <td>
                                {{ Str::limit($sector->sector_desc, 50) ?? '-' }}
                            </td>
                            <td>
                                @if($sector->is_active)
                                    <span style="color: #27ae60; font-weight: bold;">
                                        <i class="fas fa-check-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                        Active
                                    </span>
                                @else
                                    <span style="color: #e74c3c; font-weight: bold;">
                                        <i class="fas fa-times-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                        Inactive
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div style="display: flex; gap: 2px;">
                                    <button class="winforms-button-success"
                                            wire:click="showEditForm({{ $sector->sector_id }})"
                                            title="Edit sector">
                                        <i class="fas fa-edit" style="font-size: 9px;"></i>
                                    </button>
                                    <button class="winforms-button-danger"
                                            wire:click="confirmDelete({{ $sector->sector_id }})"
                                            title="Delete sector">
                                        <i class="fas fa-trash" style="font-size: 9px;"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <!-- Custom WinForms Pagination -->
            <div class="winforms-custom-pagination">
                @if($sectors->hasPages())
                    <div class="winforms-pagination-container">
                        <!-- Page Info -->
                        <div class="winforms-page-info">
                            <span>Page {{ $sectors->currentPage() }} of {{ $sectors->lastPage() }}</span>
                            <span class="winforms-total-records">({{ $sectors->total() }} total records)</span>
                        </div>

                        <!-- Pagination Controls -->
                        <div class="winforms-pagination-controls">
                            <!-- First Page Button -->
                            @if($sectors->currentPage() > 1)
                                <button class="winforms-pagination-btn"
                                        wire:click="goToPage(1)"
                                        title="First page">
                                    <i class="fas fa-angle-double-left" style="font-size: 9px;"></i>
                                    First
                                </button>
                            @else
                                <button class="winforms-pagination-btn disabled" disabled title="First page">
                                    <i class="fas fa-angle-double-left" style="font-size: 9px;"></i>
                                    First
                                </button>
                            @endif

                            <!-- Previous Page Button -->
                            @if($sectors->currentPage() > 1)
                                <button class="winforms-pagination-btn"
                                        wire:click="goPreviousPage"
                                        title="Previous page">
                                    <i class="fas fa-angle-left" style="font-size: 9px;"></i>
                                    Prev
                                </button>
                            @else
                                <button class="winforms-pagination-btn disabled" disabled title="Previous page">
                                    <i class="fas fa-angle-left" style="font-size: 9px;"></i>
                                    Prev
                                </button>
                            @endif

                            <!-- Page Number Buttons -->
                            @php
                                $currentPage = $sectors->currentPage();
                                $lastPage = $sectors->lastPage();
                                $startPage = max(1, $currentPage - 2);
                                $endPage = min($lastPage, $currentPage + 2);

                                // Adjust range to always show 5 pages when possible
                                if ($endPage - $startPage < 4) {
                                    if ($startPage == 1) {
                                        $endPage = min($lastPage, $startPage + 4);
                                    } else {
                                        $startPage = max(1, $endPage - 4);
                                    }
                                }
                            @endphp

                            @if($startPage > 1)
                                <span class="winforms-pagination-ellipsis">...</span>
                            @endif

                            @for($page = $startPage; $page <= $endPage; $page++)
                                @if($page == $currentPage)
                                    <button class="winforms-pagination-btn active" disabled>
                                        {{ $page }}
                                    </button>
                                @else
                                    <button class="winforms-pagination-btn"
                                            wire:click="goToPage({{ $page }})"
                                            title="Go to page {{ $page }}">
                                        {{ $page }}
                                    </button>
                                @endif
                            @endfor

                            @if($endPage < $lastPage)
                                <span class="winforms-pagination-ellipsis">...</span>
                            @endif

                            <!-- Next Page Button -->
                            @if($sectors->hasMorePages())
                                <button class="winforms-pagination-btn"
                                        wire:click="goNextPage"
                                        title="Next page">
                                    Next
                                    <i class="fas fa-angle-right" style="font-size: 9px;"></i>
                                </button>
                            @else
                                <button class="winforms-pagination-btn disabled" disabled title="Next page">
                                    Next
                                    <i class="fas fa-angle-right" style="font-size: 9px;"></i>
                                </button>
                            @endif

                            <!-- Last Page Button -->
                            @if($sectors->currentPage() < $sectors->lastPage())
                                <button class="winforms-pagination-btn"
                                        wire:click="goToPage({{ $sectors->lastPage() }})"
                                        title="Last page">
                                    Last
                                    <i class="fas fa-angle-double-right" style="font-size: 9px;"></i>
                                </button>
                            @else
                                <button class="winforms-pagination-btn disabled" disabled title="Last page">
                                    Last
                                    <i class="fas fa-angle-double-right" style="font-size: 9px;"></i>
                                </button>
                            @endif
                        </div>

                        <!-- Records Per Page Info -->
                        <div class="winforms-records-info">
                            <span>Showing {{ $sectors->firstItem() ?? 0 }} to {{ $sectors->lastItem() ?? 0 }} of {{ $sectors->total() }} entries</span>
                        </div>
                    </div>
                @endif
            </div>
        @else
            <div style="text-align: center; padding: 32px; color: #666;">
                <i class="fas fa-inbox" style="font-size: 32px; margin-bottom: 8px; display: block;"></i>
                @if($search)
                    <p>No sectors found matching "{{ $search }}"</p>
                    <button class="winforms-button" wire:click="$set('search', '')">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Clear Search
                    </button>
                @else
                    <p>No sectors available</p>
                    <button class="winforms-button-primary" wire:click="showCreateForm">
                        <i class="fas fa-plus" style="margin-right: 4px; font-size: 10px;"></i>
                        Add First Sector
                    </button>
                @endif
            </div>
        @endif
    </div>

    <!-- Delete Confirmation Modal -->
    @if($confirmingDeletion && $sectorToDelete)
        <div class="winforms-modal">
            <div class="winforms-modal-content">
                <div class="winforms-modal-header">
                    <i class="fas fa-exclamation-triangle" style="margin-right: 6px; color: #e74c3c;"></i>
                    Confirm Deletion
                </div>

                <div style="padding: 8px 0;">
                    <p style="margin: 0 0 12px 0; font-size: 11px; line-height: 1.4;">
                        Are you sure you want to delete the following sector?
                    </p>

                    <div style="background: #f8f8f8; border: 1px inset #c0c0c0; padding: 8px; margin: 8px 0;">
                        <div style="font-weight: bold; margin-bottom: 4px;">
                            {{ $sectorToDelete->sector }}
                        </div>
                        @if($sectorToDelete->sector_desc)
                            <div style="color: #666; font-size: 10px;">
                                {{ $sectorToDelete->sector_desc }}
                            </div>
                        @endif
                    </div>

                    <p style="margin: 12px 0 0 0; font-size: 10px; color: #e74c3c;">
                        <i class="fas fa-warning" style="margin-right: 4px;"></i>
                        This action cannot be undone.
                    </p>
                </div>

                <div style="display: flex; justify-content: flex-end; gap: 6px; margin-top: 16px;">
                    <button class="winforms-button-danger" wire:click="delete">
                        <i class="fas fa-trash" style="margin-right: 4px; font-size: 10px;"></i>
                        Delete
                    </button>
                    <button class="winforms-button" wire:click="cancelDelete">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Custom Pagination Styles -->
    <style>
        /* Custom WinForms Pagination Styles */
        .winforms-custom-pagination {
            margin-top: 12px;
            padding: 8px;
            background: #ece9d8;
            border: 1px inset #c0c0c0;
        }

        .winforms-pagination-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .winforms-page-info {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 11px;
            color: #333;
            font-weight: bold;
        }

        .winforms-total-records {
            color: #666;
            font-weight: normal;
            font-size: 10px;
        }

        .winforms-pagination-controls {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .winforms-pagination-btn {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            color: #333;
            padding: 3px 8px;
            cursor: pointer;
            font-size: 10px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-width: 24px;
            text-align: center;
            transition: all 0.1s ease;
        }

        .winforms-pagination-btn:hover:not(.disabled):not(.active) {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
            border: 1px outset #b0b0b0;
        }

        .winforms-pagination-btn:active:not(.disabled):not(.active) {
            background: linear-gradient(to bottom, #e0e0e0, #f8f8f8);
            border: 1px inset #c0c0c0;
        }

        .winforms-pagination-btn.active {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px inset #0078d4;
            color: white;
            font-weight: bold;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .winforms-pagination-btn.disabled {
            background: #f0f0f0;
            border: 1px outset #d0d0d0;
            color: #999;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .winforms-pagination-btn.disabled:hover {
            background: #f0f0f0;
            border: 1px outset #d0d0d0;
        }

        .winforms-pagination-ellipsis {
            color: #666;
            font-size: 10px;
            padding: 3px 4px;
            font-weight: bold;
        }

        .winforms-records-info {
            font-size: 10px;
            color: #666;
            text-align: center;
            padding: 4px 8px;
            background: #f8f8f8;
            border: 1px inset #c0c0c0;
            border-radius: 2px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .winforms-pagination-container {
                gap: 6px;
            }

            .winforms-pagination-controls {
                flex-wrap: wrap;
                justify-content: center;
            }

            .winforms-page-info {
                flex-direction: column;
                gap: 4px;
                text-align: center;
            }
        }

        /* Edit Form Flash Effect */
        .winforms-form-flash {
            animation: winforms-edit-flash 0.8s ease-in-out;
        }

        @keyframes winforms-edit-flash {
            0% {
                border: 2px inset #c0c0c0;
                background: #ece9d8;
                box-shadow: none;
            }
            15% {
                border: 2px solid #4a9eff;
                background: #f0f8ff;
                box-shadow: 0 0 8px rgba(74, 158, 255, 0.4), inset 0 0 8px rgba(74, 158, 255, 0.1);
            }
            30% {
                border: 2px solid #0078d4;
                background: #e6f3ff;
                box-shadow: 0 0 12px rgba(0, 120, 212, 0.5), inset 0 0 12px rgba(0, 120, 212, 0.15);
            }
            45% {
                border: 2px solid #4a9eff;
                background: #f0f8ff;
                box-shadow: 0 0 8px rgba(74, 158, 255, 0.4), inset 0 0 8px rgba(74, 158, 255, 0.1);
            }
            60% {
                border: 2px solid #87ceeb;
                background: #f8fcff;
                box-shadow: 0 0 6px rgba(135, 206, 235, 0.3), inset 0 0 6px rgba(135, 206, 235, 0.1);
            }
            100% {
                border: 2px inset #c0c0c0;
                background: #ece9d8;
                box-shadow: none;
            }
        }

        /* Subtle pulse effect for form title during flash */
        .winforms-form-title-flash {
            animation: winforms-title-pulse 0.8s ease-in-out;
        }

        @keyframes winforms-title-pulse {
            0%, 100% {
                color: #333;
                text-shadow: none;
            }
            30% {
                color: #0078d4;
                text-shadow: 0 0 2px rgba(0, 120, 212, 0.3);
            }
        }
    </style>

    <script>
        // Flash effect management
        document.addEventListener('livewire:init', () => {
            Livewire.on('reset-flash-effect', () => {
                // Reset flash effect after animation duration
                setTimeout(() => {
                    @this.set('flashEffect', false);
                }, 800);
            });
        });

        // Enhanced visual feedback for edit mode
        document.addEventListener('DOMContentLoaded', function() {
            // Add subtle sound effect simulation (visual feedback)
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const target = mutation.target;
                        if (target.classList.contains('winforms-form-flash')) {
                            // Add a subtle vibration effect to the form inputs during flash
                            const inputs = target.querySelectorAll('.winforms-textbox');
                            inputs.forEach(input => {
                                input.style.transition = 'all 0.1s ease-in-out';
                                setTimeout(() => {
                                    input.style.transform = 'translateX(1px)';
                                    setTimeout(() => {
                                        input.style.transform = 'translateX(-1px)';
                                        setTimeout(() => {
                                            input.style.transform = 'translateX(0)';
                                            input.style.transition = '';
                                        }, 50);
                                    }, 50);
                                }, 200);
                            });
                        }
                    }
                });
            });

            // Observe the form container for class changes
            const formContainer = document.querySelector('.winforms-groupbox');
            if (formContainer) {
                observer.observe(formContainer, { attributes: true, subtree: true });
            }
        });
    </script>
</div>
