<div class="winforms-main-content">
    <!-- Page Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h2 style="margin: 0; font-size: 16px; color: #333; font-weight: bold;">
            <i class="fas fa-shield-alt" style="margin-right: 6px; color: #0078d4;"></i>
            Employer Verification Management
        </h2>
        <div style="font-size: 10px; color: #666;">
            <i class="fas fa-clock" style="margin-right: 4px;"></i>
            Last Updated: {{ now()->format('Y-m-d H:i:s') }}
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="winforms-success">
            <i class="fas fa-check-circle" style="margin-right: 6px;"></i>
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="winforms-error-message">
            <i class="fas fa-exclamation-triangle" style="margin-right: 6px;"></i>
            {{ session('error') }}
        </div>
    @endif

    <!-- Search and Filter Controls -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Search & Filter</div>
        
        <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
            <!-- Search Box -->
            <div class="winforms-search-box" style="flex: 1; min-width: 200px;">
                <label style="font-weight: bold; font-size: 11px; color: #333; margin-right: 6px;">Search:</label>
                <input type="text" 
                       wire:model.live.debounce.300ms="search" 
                       class="winforms-textbox" 
                       placeholder="Company name, industry, or email..."
                       style="flex: 1;">
                <i class="fas fa-search" style="margin-left: 6px; color: #666; font-size: 10px;"></i>
            </div>

            <!-- Status Filter -->
            <div style="display: flex; align-items: center; gap: 6px;">
                <label style="font-weight: bold; font-size: 11px; color: #333;">Status:</label>
                <select wire:model.live="statusFilter" class="winforms-textbox" style="width: 180px;">
                    @foreach($statusOptions as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    <!-- Statistics Summary -->
    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #0078d4;">{{ $employers->total() }}</div>
            <div style="font-size: 10px; color: #666;">Total Employers</div>
        </div>
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #27ae60;">
                {{ \App\Models\Employer::where('is_verified', true)->count() }}
            </div>
            <div style="font-size: 10px; color: #666;">Verified</div>
        </div>
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #f39c12;">
                {{ \App\Models\VerificationDocument::where('status', \App\Models\VerificationDocument::STATUS_PENDING)->count() }}
            </div>
            <div style="font-size: 10px; color: #666;">Pending Review</div>
        </div>
        <div style="flex: 1; padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8; text-align: center;">
            <div style="font-size: 18px; font-weight: bold; color: #e74c3c;">
                {{ \App\Models\VerificationDocument::where('status', \App\Models\VerificationDocument::STATUS_REJECTED)->count() }}
            </div>
            <div style="font-size: 10px; color: #666;">Rejected</div>
        </div>
    </div>

    <!-- Employers Data Grid -->
    <div class="winforms-groupbox">
        <div class="winforms-groupbox-title">Employer Verification Status</div>
        
        @if($employers->count() > 0)
            <table class="winforms-datagrid">
                <thead>
                    <tr>
                        <th style="width: 50px;">ID</th>
                        <th>Company Information</th>
                        <th style="width: 120px;">Industry</th>
                        <th style="width: 100px;">Verification Status</th>
                        <th style="width: 80px;">Documents</th>
                        <th style="width: 120px;">Registration Date</th>
                        <th style="width: 150px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($employers as $employer)
                        <tr class="{{ $flashEffect ? 'winforms-form-flash' : '' }}">
                            <td>{{ $employer->id }}</td>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 2px;">
                                    <strong>{{ $employer->company_name }}</strong>
                                    <div style="font-size: 10px; color: #666;">
                                        <i class="fas fa-envelope" style="margin-right: 2px;"></i>
                                        {{ $employer->user->email ?? 'N/A' }}
                                    </div>
                                    @if($employer->phone)
                                        <div style="font-size: 10px; color: #666;">
                                            <i class="fas fa-phone" style="margin-right: 2px;"></i>
                                            {{ $employer->phone }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td>{{ Str::limit($employer->industry, 15) ?? '-' }}</td>
                            <td>
                                @if($employer->is_verified)
                                    <span style="color: #27ae60; font-weight: bold;">
                                        <i class="fas fa-check-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                        Verified
                                    </span>
                                @else
                                    @php
                                        $pendingDocs = $employer->documents()->where('status', \App\Models\VerificationDocument::STATUS_PENDING)->count();
                                        $rejectedDocs = $employer->documents()->where('status', \App\Models\VerificationDocument::STATUS_REJECTED)->count();
                                    @endphp
                                    @if($pendingDocs > 0)
                                        <span style="color: #f39c12; font-weight: bold;">
                                            <i class="fas fa-clock" style="font-size: 10px; margin-right: 2px;"></i>
                                            Pending
                                        </span>
                                    @elseif($rejectedDocs > 0)
                                        <span style="color: #e74c3c; font-weight: bold;">
                                            <i class="fas fa-times-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                            Rejected
                                        </span>
                                    @else
                                        <span style="color: #95a5a6; font-weight: bold;">
                                            <i class="fas fa-question-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                            No Docs
                                        </span>
                                    @endif
                                @endif
                            </td>
                            <td style="text-align: center;">
                                <span style="background: #e8e8e8; padding: 2px 6px; border-radius: 2px; font-size: 10px;">
                                    {{ $employer->documents()->count() }}
                                </span>
                            </td>
                            <td style="font-size: 10px;">
                                {{ $employer->created_at ? $employer->created_at->format('Y-m-d') : 'N/A' }}
                            </td>
                            <td>
                                <div style="display: flex; gap: 2px; flex-wrap: wrap;">
                                    <button class="winforms-button" 
                                            wire:click="showDocuments({{ $employer->id }})"
                                            title="View Documents">
                                        <i class="fas fa-file-alt" style="font-size: 9px;"></i>
                                    </button>
                                    
                                    @if(!$employer->is_verified)
                                        <button class="winforms-button-success" 
                                                wire:click="approveEmployer({{ $employer->id }})"
                                                title="Approve Employer"
                                                onclick="return confirm('Are you sure you want to approve this employer?')">
                                            <i class="fas fa-check" style="font-size: 9px;"></i>
                                        </button>
                                        
                                        <button class="winforms-button-danger" 
                                                wire:click="rejectEmployer({{ $employer->id }})"
                                                title="Reject Employer"
                                                onclick="return confirm('Are you sure you want to reject this employer?')">
                                            <i class="fas fa-times" style="font-size: 9px;"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div style="text-align: center; padding: 32px; color: #666;">
                <i class="fas fa-search" style="font-size: 32px; margin-bottom: 8px; opacity: 0.5;"></i>
                <div style="font-size: 12px;">No employers found matching your criteria.</div>
            </div>
        @endif
    </div>

    <!-- Custom WinForms Pagination -->
    @if($employers->hasPages())
        <div class="winforms-custom-pagination">
            <div class="winforms-pagination-container">
                <div class="winforms-page-info">
                    <span>Page {{ $employers->currentPage() }} of {{ $employers->lastPage() }}</span>
                    <span class="winforms-total-records">
                        ({{ $employers->firstItem() }}-{{ $employers->lastItem() }} of {{ $employers->total() }} employers)
                    </span>
                </div>

                <div class="winforms-pagination-controls">
                    <button class="winforms-pagination-btn"
                            wire:click="goToPage(1)"
                            @if($employers->currentPage() == 1) disabled @endif
                            title="First Page">
                        <i class="fas fa-angle-double-left"></i>
                    </button>

                    <button class="winforms-pagination-btn"
                            wire:click="goPreviousPage"
                            @if(!$employers->previousPageUrl()) disabled @endif
                            title="Previous Page">
                        <i class="fas fa-angle-left"></i>
                    </button>

                    @for($i = max(1, $employers->currentPage() - 2); $i <= min($employers->lastPage(), $employers->currentPage() + 2); $i++)
                        <button class="winforms-pagination-btn {{ $i == $employers->currentPage() ? 'active' : '' }}"
                                wire:click="goToPage({{ $i }})">
                            {{ $i }}
                        </button>
                    @endfor

                    <button class="winforms-pagination-btn"
                            wire:click="goNextPage"
                            @if(!$employers->nextPageUrl()) disabled @endif
                            title="Next Page">
                        <i class="fas fa-angle-right"></i>
                    </button>

                    <button class="winforms-pagination-btn"
                            wire:click="goToPage({{ $employers->lastPage() }})"
                            @if($employers->currentPage() == $employers->lastPage()) disabled @endif
                            title="Last Page">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Document Modal -->
    @if($showDocumentModal && $selectedEmployer)
        <div class="winforms-modal">
            <div class="winforms-modal-content" style="max-width: 800px; width: 90%;">
                <div class="winforms-modal-header {{ $flashEffect ? 'winforms-form-title-flash' : '' }}">
                    <i class="fas fa-file-alt" style="margin-right: 6px;"></i>
                    Verification Documents - {{ $selectedEmployer->company_name }}
                </div>

                <div style="margin-bottom: 16px;">
                    <div class="winforms-groupbox">
                        <div class="winforms-groupbox-title">Employer Information</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 11px;">
                            <div>
                                <strong>Company:</strong> {{ $selectedEmployer->company_name }}<br>
                                <strong>Industry:</strong> {{ $selectedEmployer->industry ?? 'N/A' }}<br>
                                <strong>Email:</strong> {{ $selectedEmployer->user->email ?? 'N/A' }}
                            </div>
                            <div>
                                <strong>Phone:</strong> {{ $selectedEmployer->phone ?? 'N/A' }}<br>
                                <strong>Status:</strong>
                                <span style="color: {{ $selectedEmployer->is_verified ? '#27ae60' : '#e74c3c' }}; font-weight: bold;">
                                    {{ $this->verificationStatus }}
                                </span><br>
                                <strong>Registered:</strong> {{ $selectedEmployer->created_at ? $selectedEmployer->created_at->format('Y-m-d H:i') : 'N/A' }}
                            </div>
                        </div>
                    </div>
                </div>

                @if($selectedEmployer->documents->count() > 0)
                    <div class="winforms-groupbox">
                        <div class="winforms-groupbox-title">Documents ({{ $selectedEmployer->documents->count() }})</div>
                        <table class="winforms-datagrid">
                            <thead>
                                <tr>
                                    <th>Document Type</th>
                                    <th>Status</th>
                                    <th>Submission Date</th>
                                    <th>Verification Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($selectedEmployer->documents as $document)
                                    <tr class="{{ $selectedDocument && $selectedDocument->id == $document->id ? 'selected-row' : '' }}">
                                        <td>{{ ucfirst($document->document_type) }}</td>
                                        <td>
                                            @switch($document->status)
                                                @case(\App\Models\VerificationDocument::STATUS_PENDING)
                                                    <span style="color: #f39c12; font-weight: bold;">
                                                        <i class="fas fa-clock" style="font-size: 10px; margin-right: 2px;"></i>
                                                        Pending
                                                    </span>
                                                    @break
                                                @case(\App\Models\VerificationDocument::STATUS_APPROVED)
                                                    <span style="color: #27ae60; font-weight: bold;">
                                                        <i class="fas fa-check-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                                        Approved
                                                    </span>
                                                    @break
                                                @case(\App\Models\VerificationDocument::STATUS_REJECTED)
                                                    <span style="color: #e74c3c; font-weight: bold;">
                                                        <i class="fas fa-times-circle" style="font-size: 10px; margin-right: 2px;"></i>
                                                        Rejected
                                                    </span>
                                                    @break
                                                @default
                                                    <span style="color: #95a5a6;">{{ \App\Models\VerificationDocument::getStatusDisplayName($document->status) }}</span>
                                            @endswitch
                                        </td>
                                        <td style="font-size: 10px;">
                                            {{ $document->submission_date ? \Carbon\Carbon::parse($document->submission_date)->format('Y-m-d H:i') : 'N/A' }}
                                        </td>
                                        <td style="font-size: 10px;">
                                            {{ $document->verification_date ? \Carbon\Carbon::parse($document->verification_date)->format('Y-m-d H:i') : '-' }}
                                        </td>
                                        <td>
                                            <div style="display: flex; gap: 2px;">
                                                <button class="winforms-button"
                                                        wire:click="selectDocument({{ $document->id }})"
                                                        title="Select for Review">
                                                    <i class="fas fa-eye" style="font-size: 9px;"></i>
                                                </button>

                                                    <button class="winforms-button"
                                                            wire:click="fpreviewDocument({{ $document->id }})"
                                                            title="Preview Document"
                                                            style="background: linear-gradient(to bottom, #e6f3ff, #cce7ff);">
                                                        <i class="fas fa-search" style="font-size: 9px; color: #0078d4;"></i>
                                                    </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div style="text-align: center; padding: 24px; color: #666; border: 2px inset #c0c0c0; background: #f8f8f8;">
                        <i class="fas fa-file-alt" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
                        <div>No documents uploaded yet.</div>
                    </div>
                @endif

                <!-- Document Verification Form -->
                @if($selectedDocument)
                    <div class="winforms-groupbox">
                        <div class="winforms-groupbox-title">Verify Selected Document</div>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div style="padding: 8px; border: 2px inset #c0c0c0; background: #f8f8f8;">
                                <strong>Document:</strong> {{ ucfirst($selectedDocument->document_type) }}<br>
                                <strong>Current Status:</strong> {{ ucfirst($selectedDocument->status) }}<br>
                                @if($selectedDocument->notes)
                                    <strong>Previous Notes:</strong> {{ $selectedDocument->notes }}
                                @endif
                            </div>

                            <div class="winforms-form-row">
                                <label class="winforms-form-label">Action:</label>
                                <div class="winforms-form-input">
                                    <select wire:model.live="verificationAction" class="winforms-textbox">
                                        <option value="">Select Action</option>
                                        <option value="{{ \App\Models\VerificationDocument::STATUS_APPROVED }}">Approve Document</option>
                                        <option value="{{ \App\Models\VerificationDocument::STATUS_REJECTED }}">Reject Document</option>
                                        <option value="{{ \App\Models\VerificationDocument::STATUS_PENDING }}">Mark as Pending</option>
                                        <option value="{{ \App\Models\VerificationDocument::STATUS_INCOMPLETE }}">Mark as Incomplete</option>
                                    </select>
                                </div>
                            </div>

                            <div class="winforms-form-row">
                                <label class="winforms-form-label">Notes:</label>
                                <div class="winforms-form-input">
                                    <textarea wire:model="verificationNotes"
                                              class="winforms-textbox"
                                              rows="3"
                                              placeholder="Add verification notes..."></textarea>
                                </div>
                            </div>

                            <div style="display: flex; gap: 8px; justify-content: flex-end;">
                                <button class="winforms-button-primary"
                                        wire:click="processVerification"
                                        @if(!$verificationAction) disabled @endif
                                        title="Process Verification">
                                    <i class="fas fa-check" style="margin-right: 4px; font-size: 10px;"></i>
                                    Process Verification
                                </button>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Modal Footer -->
                <div style="display: flex; justify-content: flex-end; gap: 8px; margin-top: 16px; padding-top: 12px; border-top: 1px solid #c0c0c0;">
                    <button class="winforms-button" wire:click="closeDocumentModal">
                        <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                        Close
                    </button>
                </div>
            </div>
        </div>
    @endif

    <!-- Document Preview Modal -->
    @if($showPreviewModal && $previewDocument)
        <div class="winforms-modal" style="z-index: 1100;">
            <div class="winforms-modal-content" style="max-width: 95%; max-height: 95%; width: 1200px; height: 800px; display: flex; flex-direction: column;">
                <div class="winforms-modal-header">
                    <i class="fas fa-search" style="margin-right: 6px;"></i>
                    Document Preview - {{ ucfirst($previewDocument->document_type) }}
                </div>

                <!-- Preview Controls -->
                <div style="padding: 8px; border-bottom: 1px solid #c0c0c0; background: #f0f0f0; display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <span style="font-size: 11px; font-weight: bold;">Zoom:</span>
                        <button class="winforms-button" wire:click="zoomOut" title="Zoom Out">
                            <i class="fas fa-search-minus" style="font-size: 9px;"></i>
                        </button>
                        <span style="font-size: 11px; min-width: 40px; text-align: center;">{{ $previewZoom }}%</span>
                        <button class="winforms-button" wire:click="zoomIn" title="Zoom In">
                            <i class="fas fa-search-plus" style="font-size: 9px;"></i>
                        </button>
                        <button class="winforms-button" wire:click="resetZoom" title="Reset Zoom">
                            <i class="fas fa-expand-arrows-alt" style="font-size: 9px;"></i>
                        </button>
                    </div>

                    <div style="font-size: 11px; color: #666;">
                        <strong>File:</strong> {{ basename($previewDocument->file_path) }}
                    </div>
                </div>

                <!-- Preview Content -->
                <div style="flex: 1; overflow: auto; background: #f8f8f8; border: 2px inset #c0c0c0; position: relative;">
                    @if($previewLoading)
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column;">
                            <div class="winforms-loading-spinner"></div>
                            <div style="margin-top: 12px; font-size: 11px; color: #666;">Loading document...</div>
                        </div>
                    @elseif($previewError)
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column; color: #e74c3c;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 32px; margin-bottom: 12px;"></i>
                            <div style="font-size: 12px; font-weight: bold; margin-bottom: 8px;">Preview Error</div>
                            <div style="font-size: 11px; text-align: center; max-width: 400px;">{{ $previewError }}</div>
                        </div>
                    @else
                        @php
                            $filePath = $previewDocument->file_path;
                            $documentType = $this->getDocumentType($filePath);
                            $documentUrl = $filePath;
                            $downloadUrl = $filePath;
                        @endphp

                        <div style="padding: 16px; height: 100%; display: flex; align-items: center; justify-content: center;">
                            @if($documentType === 'pdf')
                                <iframe src="{{ $documentUrl }}"
                                        style="width: {{ $previewZoom }}%; height: {{ $previewZoom }}%; border: 1px solid #c0c0c0; background: white;"
                                        title="PDF Preview">
                                </iframe>
                            @elseif($documentType === 'image')
                                <img src="{{ $documentUrl }}"
                                     alt="Document Preview"
                                     style="max-width: {{ $previewZoom }}%; max-height: {{ $previewZoom }}%; border: 1px solid #c0c0c0; background: white; object-fit: contain;">
                            @else
                                <div style="text-align: center; color: #666;">
                                    <i class="fas fa-file" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                                    <div style="font-size: 12px; font-weight: bold; margin-bottom: 8px;">Preview Not Available</div>
                                    <div style="font-size: 11px; margin-bottom: 16px;">This file type cannot be previewed in the browser.</div>
                                    <a href="{{ $previewDocument->file_path }}"
                                       target="_blank"
                                       class="winforms-button-primary"
                                       style="text-decoration: none; display: inline-block;">
                                        <i class="fas fa-download" style="margin-right: 4px; font-size: 10px;"></i>
                                        Download File
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Modal Footer -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; border-top: 1px solid #c0c0c0; background: #f0f0f0;">
                    <div style="display: flex; gap: 8px;">
                        @if(!$previewError && $previewDocument->file_path)
                            <a href="{{ $previewDocument->file_path }}"
                               target="_blank"
                               class="winforms-button"
                               style="text-decoration: none;">
                                <i class="fas fa-external-link-alt" style="margin-right: 4px; font-size: 10px;"></i>
                                Open in New Tab
                            </a>

                            <a href="{{ $previewDocument->file_path }}"
                               class="winforms-button"
                               style="text-decoration: none;">
                                <i class="fas fa-download" style="margin-right: 4px; font-size: 10px;"></i>
                                Download
                            </a>
                        @endif
                    </div>

                    <div style="display: flex; gap: 8px;">
                        @if($previewDocument && !$selectedDocument)
                            <button class="winforms-button-primary"
                                    wire:click="selectDocument({{ $previewDocument->id }})"
                                    title="Select this document for verification">
                                <i class="fas fa-check" style="margin-right: 4px; font-size: 10px;"></i>
                                Select for Verification
                            </button>
                        @endif

                        <button class="winforms-button" wire:click="closePreviewModal">
                            <i class="fas fa-times" style="margin-right: 4px; font-size: 10px;"></i>
                            Close Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Custom Pagination Styles -->
    <style>
        /* Custom WinForms Pagination Styles */
        .winforms-custom-pagination {
            margin-top: 12px;
            padding: 8px;
            background: #ece9d8;
            border: 1px inset #c0c0c0;
        }

        .winforms-pagination-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .winforms-page-info {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 11px;
            color: #333;
            font-weight: bold;
        }

        .winforms-total-records {
            color: #666;
            font-weight: normal;
            font-size: 10px;
        }

        .winforms-pagination-controls {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .winforms-pagination-btn {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 10px;
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.1s;
        }

        .winforms-pagination-btn:hover:not(:disabled) {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
        }

        .winforms-pagination-btn:active:not(:disabled) {
            border: 1px inset #c0c0c0;
            background: linear-gradient(to bottom, #e0e0e0, #f8f8f8);
            transform: translateY(1px);
        }

        .winforms-pagination-btn:disabled {
            background: #f0f0f0;
            color: #999;
            cursor: not-allowed;
            border: 1px solid #d0d0d0;
        }

        .winforms-pagination-btn.active {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            color: white;
            border: 1px inset #0078d4;
            font-weight: bold;
        }

        /* Selected row highlighting */
        .selected-row {
            background: #e6f3ff !important;
            border: 1px solid #0078d4 !important;
        }

        /* Flash effect for verification actions */
        .winforms-form-flash {
            animation: winforms-verification-flash 0.8s ease-in-out;
        }

        @keyframes winforms-verification-flash {
            0%, 100% {
                background: #ece9d8;
                border-color: #c0c0c0;
            }
            25% {
                background: #e6f3ff;
                border-color: #0078d4;
            }
            50% {
                background: #d4edda;
                border-color: #27ae60;
            }
            75% {
                background: #e6f3ff;
                border-color: #0078d4;
            }
        }

        .winforms-form-title-flash {
            animation: winforms-title-pulse 0.8s ease-in-out;
        }

        @keyframes winforms-title-pulse {
            0%, 100% {
                color: #333;
                text-shadow: none;
            }
            30% {
                color: #0078d4;
                text-shadow: 0 0 2px rgba(0, 120, 212, 0.3);
            }
        }

        /* Document Preview Styles */
        .winforms-loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f0f0f0;
            border-top: 3px solid #0078d4;
            border-radius: 50%;
            animation: winforms-spin 1s linear infinite;
        }

        @keyframes winforms-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Preview modal specific styles */
        .winforms-modal[style*="z-index: 1100"] {
            background: rgba(0, 0, 0, 0.7);
        }

        .winforms-modal[style*="z-index: 1100"] .winforms-modal-content {
            box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.5);
        }

        /* Preview content scrollbars */
        .winforms-modal iframe {
            scrollbar-width: thin;
            scrollbar-color: #c0c0c0 #f0f0f0;
        }

        .winforms-modal iframe::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        .winforms-modal iframe::-webkit-scrollbar-track {
            background: #f0f0f0;
            border: 1px inset #c0c0c0;
        }

        .winforms-modal iframe::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #e0e0e0, #c0c0c0);
            border: 1px outset #c0c0c0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .winforms-pagination-container {
                gap: 6px;
            }

            .winforms-pagination-controls {
                flex-wrap: wrap;
                justify-content: center;
            }

            .winforms-page-info {
                flex-direction: column;
                gap: 4px;
                text-align: center;
            }

            /* Preview modal responsive */
            .winforms-modal[style*="z-index: 1100"] .winforms-modal-content {
                width: 95% !important;
                height: 90% !important;
                max-width: none !important;
                max-height: none !important;
                margin: 20px auto !important;
            }
        }
    </style>

    <script>
        // Flash effect management
        document.addEventListener('livewire:init', () => {
            Livewire.on('reset-flash-effect', () => {
                // Reset flash effect after animation duration
                setTimeout(() => {
                    @this.set('flashEffect', false);
                }, 800);
            });
        });
    </script>
</div>
