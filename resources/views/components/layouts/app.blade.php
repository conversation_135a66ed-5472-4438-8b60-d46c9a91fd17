<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title ?? 'CariKerja Dashboard' }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom WinForms-style CSS */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            background: #ece9d8;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        #nprogress .bar {
            height: 6px !important;  
            background: #316ac5 !important;
        }

        .winforms-window {
            background: #ece9d8;
            border: 2px outset #c0c0c0;
            box-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .winforms-header {
            background: linear-gradient(to bottom, #d4d0c8, #c0c0c0);
            border-bottom: 2px outset #c0c0c0;
            padding: 8px 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            font-size: 14px;
            color: #333;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .winforms-menubar {
            background: linear-gradient(to bottom, #f8f8f8, #e8e8e8);
            border-bottom: 1px solid #c0c0c0;
            padding: 2px 8px;
            display: flex;
            gap: 16px;
        }

        .winforms-menu-item {
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 2px;
            transition: background-color 0.1s;
        }

        .winforms-menu-item:hover {
            background-color: #316ac5;
            color: white;
        }

        .winforms-sidebar {
            background: #ece9d8;
            border-right: 2px inset #c0c0c0;
            width: 200px;
            flex-shrink: 0;
            overflow-y: auto;
            padding: 4px;
        }

        .winforms-tree-section {
            margin-bottom: 8px;
        }

        .winforms-tree-header {
            background: linear-gradient(to bottom, #d4d0c8, #c0c0c0);
            border: 1px outset #c0c0c0;
            padding: 2px 6px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .winforms-tree-header:hover {
            background: linear-gradient(to bottom, #e0e0e0, #d0d0d0);
        }

        .winforms-tree-content {
            padding-left: 16px;
            background: #ece9d8;
        }

        .winforms-tree-node {
            padding: 2px 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            border-radius: 1px;
            margin: 1px 0;
        }

        .winforms-tree-node:hover {
            background: #316ac5;
            color: white;
        }

        .winforms-tree-node.selected {
            background: #316ac5;
            color: white;
        }

        .winforms-main-content {
            flex: 1;
            background: #ece9d8;
            border: 2px inset #c0c0c0;
            margin: 4px;
            padding: 8px;
            overflow: auto;
        }

        .winforms-button {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.1s;
        }

        .winforms-button:hover {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
        }

        .winforms-button:active {
            border: 1px inset #c0c0c0;
            background: linear-gradient(to bottom, #e0e0e0, #f8f8f8);
            transform: translateY(1px);
        }

        .header-brand {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-user-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 11px;
            font-weight: normal;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 2px 6px;
            border: 1px inset #c0c0c0;
            background: #ece9d8;
            color: #333;
        }

        .user-avatar {
            width: 16px;
            height: 16px;
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px outset #0078d4;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }

        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: #ece9d8;
            border: 2px outset #c0c0c0;
            box-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            z-index: 1000;
            min-width: 140px;
            margin-top: 2px;
        }

        .user-dropdown-content.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 4px 8px;
            color: #333;
            text-decoration: none;
            font-size: 11px;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .dropdown-item:hover {
            background: #316ac5;
            color: white;
        }

        .dropdown-separator {
            height: 1px;
            background: #c0c0c0;
            margin: 2px 4px;
            border-top: 1px inset #c0c0c0;
        }

        .tree-icon {
            width: 12px;
            height: 12px;
            font-size: 8px;
            margin-top: 6px;
        }

        .app-icon {
            width: 16px;
            height: 16px;
        }

        /* Scrollbar styling for Windows look */
        ::-webkit-scrollbar {
            width: 16px;
        }

        ::-webkit-scrollbar-track {
            background: #f0f0f0;
            border: 1px inset #c0c0c0;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #e0e0e0, #c0c0c0);
            border: 1px outset #c0c0c0;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #e8e8e8, #d0d0d0);
        }

        .main-layout {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .tree-content-collapsed {
            display: none;
        }

        .winforms-groupbox {
            border: 2px inset #c0c0c0;
            background: #ece9d8;
            padding: 12px;
            margin-bottom: 12px;
            position: relative;
        }

        .winforms-groupbox-title {
            position: absolute;
            top: -8px;
            left: 8px;
            background: #ece9d8;
            padding: 0 4px;
            font-weight: bold;
            font-size: 11px;
            color: #333;
        }

        .winforms-textbox {
            border: 2px inset #c0c0c0;
            background: white;
            padding: 2px 4px;
            font-size: 11px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            width: 100%;
            box-sizing: border-box;
        }

        .winforms-textbox:focus {
            outline: none;
            border: 2px inset #0078d4;
        }

        .winforms-textbox:disabled {
            background: #f0f0f0;
            color: #666;
        }

        .winforms-checkbox {
            margin-right: 6px;
        }

        .winforms-datagrid {
            border: 2px inset #c0c0c0;
            background: white;
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .winforms-datagrid th {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 4px 6px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
            color: #333;
        }

        .winforms-datagrid td {
            border: 1px solid #e0e0e0;
            padding: 4px 6px;
            background: white;
        }

        .winforms-datagrid tr:nth-child(even) td {
            background: #f8f8f8;
        }

        .winforms-datagrid tr:hover td {
            background: #e6f3ff;
        }

        .winforms-button-group {
            display: flex;
            gap: 6px;
            margin-bottom: 12px;
        }

        .winforms-button-primary {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px outset #0078d4;
            color: white;
            padding: 4px 12px;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
        }

        .winforms-button-primary:hover {
            background: linear-gradient(to bottom, #5aa6ff, #106ebe);
        }

        .winforms-button-primary:active {
            border: 1px inset #0078d4;
            background: linear-gradient(to bottom, #0078d4, #4a9eff);
        }

        .winforms-button-danger {
            background: linear-gradient(to bottom, #ff6b6b, #e74c3c);
            border: 1px outset #e74c3c;
            color: white;
            padding: 2px 8px;
            cursor: pointer;
            font-size: 10px;
        }

        .winforms-button-danger:hover {
            background: linear-gradient(to bottom, #ff7979, #c0392b);
        }

        .winforms-button-success {
            background: linear-gradient(to bottom, #2ecc71, #27ae60);
            border: 1px outset #27ae60;
            color: white;
            padding: 2px 8px;
            cursor: pointer;
            font-size: 10px;
        }

        .winforms-button-success:hover {
            background: linear-gradient(to bottom, #58d68d, #229954);
        }

        .winforms-form-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 8px;
        }

        .winforms-form-label {
            width: 120px;
            font-weight: bold;
            font-size: 11px;
            color: #333;
            text-align: right;
        }

        .winforms-form-input {
            flex: 1;
        }

        .winforms-error {
            color: #e74c3c;
            font-size: 10px;
            margin-top: 2px;
        }

        .winforms-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 8px;
            margin-bottom: 12px;
            font-size: 11px;
        }

        .winforms-error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 8px;
            margin-bottom: 12px;
            font-size: 11px;
        }

        .winforms-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .winforms-modal-content {
            background: #ece9d8;
            border: 2px outset #c0c0c0;
            box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
            padding: 16px;
            min-width: 400px;
            max-width: 600px;
        }

        .winforms-modal-header {
            background: linear-gradient(to bottom, #d4d0c8, #c0c0c0);
            border-bottom: 1px solid #c0c0c0;
            padding: 4px 8px;
            margin: -16px -16px 12px -16px;
            font-weight: bold;
            font-size: 12px;
            color: #333;
        }

        .winforms-search-box {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
        }

        .winforms-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
            margin-top: 12px;
            font-size: 11px;
        }

        .winforms-pagination button {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 10px;
        }

        .winforms-pagination button:hover {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
        }

        .winforms-pagination button:disabled {
            background: #f0f0f0;
            color: #999;
            cursor: not-allowed;
        }

        .winforms-pagination .active {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            color: white;
            border: 1px inset #0078d4;
        }
    </style>
        
    </head>
    <body>
        <!-- Main Window Container -->
        <div class="winforms-window">
            <!-- Application Header -->
            <div class="winforms-header">
                <div class="header-brand">
                    <i class="fas fa-desktop app-icon"></i>
                    <span>CariKerja Dashboard</span>
                </div>

                <div class="header-user-controls">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>Administrator</span>
                    </div>

                    <div class="user-dropdown">
                        <button class="winforms-button" onclick="toggleUserDropdown()" id="userDropdownBtn">
                            <i class="fas fa-chevron-down" style="font-size: 9px;"></i>
                        </button>
                        <div class="user-dropdown-content" id="userDropdownContent">
                            <button class="dropdown-item" onclick="manageProfile()">
                                <i class="fas fa-user-edit" style="font-size: 10px;"></i>
                                <span>Manage Profile</span>
                            </button>
                            <button class="dropdown-item" onclick="userSettings()">
                                <i class="fas fa-cog" style="font-size: 10px;"></i>
                                <span>Settings</span>
                            </button>
                            <div class="dropdown-separator"></div>
                            <button class="dropdown-item" onclick="logout()">
                                <i class="fas fa-sign-out-alt" style="font-size: 10px;"></i>
                                <span>Logout</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Menu Bar -->
            <!-- <div class="winforms-menubar">
                <div class="winforms-menu-item">File</div>
                <div class="winforms-menu-item">Edit</div>
                <div class="winforms-menu-item">View</div>
                <div class="winforms-menu-item">Tools</div>
                <div class="winforms-menu-item">Window</div>
                <div class="winforms-menu-item">Help</div>
            </div> -->

            <!-- Main Layout Container -->
            <div class="main-layout">

                <!-- Left Sidebar -->
                <div class="winforms-sidebar">
                    <div class="winforms-tree-section">
                        <div class="winforms-tree-header" onclick="toggleSection('master')">
                            <i class="fas fa-folder-open tree-icon" id="master-icon"></i>
                            <span>Master Data</span>
                        </div>
                        <div class="winforms-tree-content" id="master-content">
                            <div class="winforms-tree-node">
                                <i class="fas fa-circle tree-icon" style="@if(request()->is('sectors')) color: #27ae60; @else color: #0078d4; @endif"></i>
                                <a href="/sectors" wire:navigate><span>Sectors</span></a>
                            </div>
                            <div class="winforms-tree-node">
                                <i class="fas fa-circle tree-icon" style="@if(request()->is('pertanyaan-preferensi')) color: #27ae60; @else color: #0078d4; @endif"></i>
                                <a href="/pertanyaan-preferensi" wire:navigate>
                                    <span>Pertanyaan Preferensi</span>
                                    </a>
                            </div>
                        </div>
                    </div>

                    <div class="winforms-tree-section">
                        <div class="winforms-tree-header" onclick="toggleSection('verification')">
                            <i class="fas fa-folder-open tree-icon" id="verification-icon"></i>
                            <span>Verification</span>
                        </div>
                        <div class="winforms-tree-content" id="verification-content">
                            <div class="winforms-tree-node">
                                <i class="fas fa-circle tree-icon" style="@if(request()->is('employer-verification')) color: #27ae60; @else color: #0078d4; @endif"></i>
                                <a href="/employer-verification" wire:navigate><span>Employer Verification</span></a>
                            </div>
                        </div>
                        <div class="winforms-tree-content" id="monitoring-content">
                            <div class="winforms-tree-node">
                                <i class="fas fa-circle tree-icon" style="@if(request()->is('job-posting-monitor')) color: #27ae60; @else color: #0078d4; @endif"></i>
                                <a href="/job-posting-monitor" wire:navigate><span>Job Posting Monitor</span></a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content Area -->
                {{ $slot }}

            </div>
        </div>

        <script>
            // Tree section toggle functionality
            function toggleSection(sectionId) {
                const content = document.getElementById(sectionId + '-content');
                const icon = document.getElementById(sectionId + '-icon');

                if (content.classList.contains('tree-content-collapsed')) {
                    content.classList.remove('tree-content-collapsed');
                    icon.className = 'fas fa-folder-open tree-icon';
                } else {
                    content.classList.add('tree-content-collapsed');
                    icon.className = 'fas fa-folder tree-icon';
                }
            }

            // Node selection functionality
            function selectNode(node) {
                // Remove selected class from all nodes
                const allNodes = document.querySelectorAll('.winforms-tree-node');
                allNodes.forEach(n => {
                    n.classList.remove('selected');
                    const icon = n.querySelector('.tree-icon');
                    if (icon) {
                        icon.style.color = '#0078d4';
                    }
                });

                // Add selected class to clicked node
                node.classList.add('selected');
                const icon = node.querySelector('.tree-icon');
                if (icon) {
                    icon.style.color = 'white';
                }

                // Update main content based on selection
                const nodeName = node.querySelector('span').textContent;
                updateMainContent(nodeName);
            }

            // Update main content area
            function updateMainContent(selectedItem) {
                const mainContent = document.querySelector('.winforms-main-content');
                mainContent.innerHTML = `
                    <h2 style="margin: 0 0 16px 0; font-size: 14px; color: #333; font-weight: bold;">${selectedItem}</h2>
                    <p style="margin: 0 0 12px 0; color: #666; line-height: 1.4;">
                        You have selected: <strong>${selectedItem}</strong>
                    </p>
                    <p style="margin: 0 0 16px 0; color: #666; line-height: 1.4;">
                        This area would typically contain forms, data grids, or other controls specific to the selected module.
                        The content dynamically updates based on your navigation selection.
                    </p>

                    <div style="margin-top: 24px; padding: 12px; border: 2px inset #c0c0c0; background: #f0f0f0;">
                        <h3 style="margin: 0 0 8px 0; font-size: 12px; font-weight: bold; color: #333;">Actions for ${selectedItem}</h3>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                            <button class="winforms-button" onclick="showMessage('View ${selectedItem}')">
                                <i class="fas fa-eye" style="margin-right: 4px; font-size: 10px;"></i>
                                View
                            </button>
                            <button class="winforms-button" onclick="showMessage('Add ${selectedItem}')">
                                <i class="fas fa-plus" style="margin-right: 4px; font-size: 10px;"></i>
                                Add New
                            </button>
                            <button class="winforms-button" onclick="showMessage('Edit ${selectedItem}')">
                                <i class="fas fa-edit" style="margin-right: 4px; font-size: 10px;"></i>
                                Edit
                            </button>
                            <button class="winforms-button" onclick="showMessage('Delete ${selectedItem}')">
                                <i class="fas fa-trash" style="margin-right: 4px; font-size: 10px;"></i>
                                Delete
                            </button>
                        </div>
                    </div>

                    <div style="margin-top: 16px; padding: 8px; border: 2px inset #c0c0c0; background: white;">
                        <div style="font-weight: bold; margin-bottom: 8px; font-size: 12px;">Module Information:</div>
                        <div style="color: #666; font-size: 11px; line-height: 1.4;">
                            Module: ${selectedItem}<br>
                            Status: Active<br>
                            Last Modified: ${new Date().toLocaleDateString()}<br>
                            Permissions: Full Access
                        </div>
                    </div>
                `;
            }

            // Show message function
            function showMessage(action) {
                alert('Action: ' + action + '\n\nThis is a demonstration of the WinForms-style interface.');
            }

            // Update current time
            function updateTime() {
                const now = new Date();
                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = now.toLocaleString();
                }
            }

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                updateTime();
                setInterval(updateTime, 1000);

                // User dropdown functionality
                document.addEventListener('click', function(event) {
                    const dropdown = document.getElementById('userDropdownContent');
                    const dropdownBtn = document.getElementById('userDropdownBtn');

                    if (!dropdownBtn.contains(event.target) && !dropdown.contains(event.target)) {
                        dropdown.classList.remove('show');
                    }
                });
            });

            // User dropdown toggle function
            function toggleUserDropdown() {
                const dropdown = document.getElementById('userDropdownContent');
                dropdown.classList.toggle('show');
            }

            // User management functions
            function manageProfile() {
                const dropdown = document.getElementById('userDropdownContent');
                dropdown.classList.remove('show');

                alert('Manage Profile\n\nThis would open the user profile management interface where you can:\n\n• Update personal information\n• Change password\n• Modify preferences\n• Update contact details');
            }

            function userSettings() {
                const dropdown = document.getElementById('userDropdownContent');
                dropdown.classList.remove('show');

                alert('User Settings\n\nThis would open the user settings panel where you can:\n\n• Configure application preferences\n• Set notification options\n• Customize interface settings\n• Manage security options');
            }

            function logout() {
                const dropdown = document.getElementById('userDropdownContent');
                dropdown.classList.remove('show');

                if (confirm('Are you sure you want to logout?\n\nYou will be redirected to the login page.')) {
                    alert('Logging out...\n\nRedirecting to login page.');
                    // In a real application, you would redirect here
                    window.location.href = 'login.html';
                }
            }
        </script>
    </body>
</html>
