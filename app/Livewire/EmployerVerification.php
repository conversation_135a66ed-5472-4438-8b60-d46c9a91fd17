<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Employer;
use App\Models\VerificationDocument;
use App\Models\VerificationDocumentHistory;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class EmployerVerification extends Component
{
    use WithPagination;

    // UI state properties
    public $search = '';
    public $statusFilter = 'all';
    public $showDocumentModal = false;
    public $selectedEmployer = null;
    public $selectedDocument = null;
    public $verificationAction = '';
    public $verificationNotes = '';
    public $flashEffect = false;

    // Document preview properties
    public $showPreviewModal = false;
    public $previewDocument = null;
    public $previewLoading = false;
    public $previewError = null;
    public $previewZoom = 100;

    // Verification status options
    public $statusOptions = [
        'all' => 'All Status',
        VerificationDocument::STATUS_PENDING => 'Pending Verification',
        VerificationDocument::STATUS_APPROVED => 'Approved',
        VerificationDocument::STATUS_REJECTED => 'Rejected',
        VerificationDocument::STATUS_INCOMPLETE => 'Incomplete Documents'
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function render()
    {
        $employers = Employer::with(['documents', 'user'])
            ->when($this->search, function ($query) {
                $query->where('company_name', 'ILIKE', '%' . $this->search . '%')
                      ->orWhere('industry', 'ILIKE', '%' . $this->search . '%')
                      ->orWhereHas('user', function ($q) {
                          $q->where('email', 'ILIKE', '%' . $this->search . '%');
                      });
            })
            ->when($this->statusFilter !== 'all', function ($query) {
                switch ($this->statusFilter) {
                    case VerificationDocument::STATUS_PENDING:
                        $query->whereHas('documents', function ($q) {
                            $q->where('status', VerificationDocument::STATUS_PENDING);
                        });
                        break;
                    case VerificationDocument::STATUS_APPROVED:
                        $query->where('is_verified', true);
                        break;
                    case VerificationDocument::STATUS_REJECTED:
                        $query->whereHas('documents', function ($q) {
                            $q->where('status', VerificationDocument::STATUS_REJECTED);
                        });
                        break;
                    case VerificationDocument::STATUS_INCOMPLETE:
                        $query->whereDoesntHave('documents')
                              ->orWhereHas('documents', function ($q) {
                                  $q->whereIn('status', [
                                      VerificationDocument::STATUS_INCOMPLETE,
                                      VerificationDocument::STATUS_MISSING
                                  ]);
                              });
                        break;
                }
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.employer-verification', [
            'employers' => $employers
        ])->layout('components.layouts.app', ['title' => 'Employer Verification']);
    }

    // Custom pagination methods
    public function goToPage($page)
    {
        $this->setPage($page);
    }

    public function goPreviousPage()
    {
        $this->previousPage();
    }

    public function goNextPage()
    {
        $this->nextPage();
    }

    public function showDocuments($employerId)
    {
        $this->selectedEmployer = Employer::with(['documents.histories', 'user'])->findOrFail($employerId);
        $this->showDocumentModal = true;
        $this->flashEffect = true;
        
        // Reset flash effect after animation completes
        $this->dispatch('reset-flash-effect');
    }

    public function closeDocumentModal()
    {
        $this->showDocumentModal = false;
        $this->selectedEmployer = null;
        $this->selectedDocument = null;
        $this->verificationAction = '';
        $this->verificationNotes = '';
        $this->flashEffect = false;
        $this->closePreviewModal(); // Also close preview if open
    }

    public function selectDocument($documentId)
    {
        $this->selectedDocument = VerificationDocument::with(['histories', 'employer'])->findOrFail($documentId);
        $this->verificationAction = '';
        $this->verificationNotes = '';
    }

    public function processVerification()
    {
        if (!$this->selectedDocument || !$this->verificationAction) {
            session()->flash('error', 'Please select a document and verification action.');
            return;
        }

        try {
            // Update document status
            $this->selectedDocument->update([
                'status' => $this->verificationAction,
                'verification_date' => now(),
                'notes' => $this->verificationNotes,
                'verified_by' => 1, // Assuming admin user ID is 1
            ]);

            // Create history record
            VerificationDocumentHistory::create([
                'verification_document_id' => $this->selectedDocument->id,
                'file_path' => $this->selectedDocument->currentHistory->file_path ?? '',
                'status_at_upload' => $this->verificationAction,
                'notes' => $this->verificationNotes,
                'verified_by' => 1,
                'verified_at' => now(),
            ]);

            // Update employer verification status if all documents are approved
            if ($this->verificationAction === VerificationDocument::STATUS_APPROVED) {
                $employer = $this->selectedDocument->employer;
                $allDocumentsApproved = $employer->documents()
                    ->where('status', '!=', VerificationDocument::STATUS_APPROVED)
                    ->count() === 0;

                if ($allDocumentsApproved) {
                    $employer->update(['is_verified' => true]);
                }
            } else {
                // If any document is rejected, mark employer as not verified
                $this->selectedDocument->employer->update(['is_verified' => false]);
            }

            session()->flash('message', 'Verification processed successfully.');
            $this->closeDocumentModal();

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while processing verification.');
        }
    }

    public function approveEmployer($employerId)
    {
        try {
            $employer = Employer::findOrFail($employerId);
            $employer->update(['is_verified' => true]);
            
            // Update all pending documents to approved
            $employer->documents()->where('status', VerificationDocument::STATUS_PENDING)->update([
                'status' => VerificationDocument::STATUS_APPROVED,
                'verification_date' => now(),
                'verified_by' => 1,
            ]);

            session()->flash('message', 'Employer approved successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while approving employer.');
        }
    }

    public function rejectEmployer($employerId)
    {
        try {
            $employer = Employer::findOrFail($employerId);
            $employer->update(['is_verified' => false]);
            
            // Update all pending documents to rejected
            $employer->documents()->where('status', VerificationDocument::STATUS_PENDING)->update([
                'status' => VerificationDocument::STATUS_REJECTED,
                'verification_date' => now(),
                'verified_by' => 1,
            ]);

            session()->flash('message', 'Employer rejected successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while rejecting employer.');
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function fpreviewDocument($documentId)
    {
        try {
            $this->previewLoading = true;
            $this->previewError = null;

            $document = VerificationDocument::with(['histories', 'currentHistory'])->findOrFail($documentId);

            // Get the latest document history with file path
            $latestHistory = $document->histories()->latest('uploaded_at')->first();

            if (!$latestHistory || !$latestHistory->file_path) {
                $this->previewError = 'No document file found for preview.';
                $this->previewLoading = false;
                return;
            }

            // Check if file exists using Storage facade
            if (!$latestHistory->file_path || !\Storage::disk('public')->exists($latestHistory->file_path)) {
                $this->previewError = 'Document file not found on server.';
                $this->previewLoading = false;
                return;
            }

            $this->previewDocument = $document;
            $this->previewDocument->latest_history = $latestHistory;
            $this->showPreviewModal = true;
            $this->previewZoom = 100;
            $this->previewLoading = false;

        } catch (\Exception $e) {
            \Log::error('Error previewing document: ' . $e->getMessage());
            $this->previewError = 'Error loading document preview: ' . $e->getMessage();
            $this->previewLoading = false;
        }
    }

    public function closePreviewModal()
    {
        $this->showPreviewModal = false;
        $this->previewDocument = null;
        $this->previewError = null;
        $this->previewLoading = false;
        $this->previewZoom = 100;
    }

    public function zoomIn()
    {
        if ($this->previewZoom < 200) {
            $this->previewZoom += 25;
        }
    }

    public function zoomOut()
    {
        if ($this->previewZoom > 50) {
            $this->previewZoom -= 25;
        }
    }

    public function resetZoom()
    {
        $this->previewZoom = 100;
    }

    public function getDocumentUrl($historyId, $inline = false)
    {
        // Generate secure URL for document access
        return route('documents.serve', ['historyId' => $historyId]) . ($inline ? '?inline=1' : '');
    }

    public function getDocumentType($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        switch ($extension) {
            case 'pdf':
                return 'pdf';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'webp':
                return 'image';
            case 'doc':
            case 'docx':
                return 'document';
            case 'xls':
            case 'xlsx':
                return 'spreadsheet';
            default:
                return 'unknown';
        }
    }

    public function getVerificationStatusProperty()
    {
        if (!$this->selectedEmployer) return 'Unknown';
        
        if ($this->selectedEmployer->is_verified) {
            return 'Verified';
        }
        
        $pendingCount = $this->selectedEmployer->documents()->where('status', VerificationDocument::STATUS_PENDING)->count();
        $rejectedCount = $this->selectedEmployer->documents()->where('status', VerificationDocument::STATUS_REJECTED)->count();
        $approvedCount = $this->selectedEmployer->documents()->where('status', VerificationDocument::STATUS_APPROVED)->count();
        
        if ($pendingCount > 0) return 'Pending Review';
        if ($rejectedCount > 0) return 'Rejected';
        if ($approvedCount > 0) return 'Partially Approved';
        
        return 'No Documents';
    }
}
