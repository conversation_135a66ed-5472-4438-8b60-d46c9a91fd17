<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\JobPosting;
use App\Models\Employer;
use Illuminate\Support\Str;

class JobPostingMonitor extends Component
{
    use WithPagination;

    // UI state properties
    public $search = '';
    public $statusFilter = 'all';
    public $locationFilter = '';
    public $jobTypeFilter = 'all';
    public $showJobModal = false;
    public $selectedJob = null;
    public $flashEffect = false;

    // Ban management properties
    public $showBanModal = false;
    public $jobToBan = null;
    public $banReason = '';
    public $confirmingBan = false;

    // Filter options
    public $statusOptions = [
        'all' => 'All Status',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'expired' => 'Expired',
        'banned' => 'Banned'
    ];

    public $jobTypeOptions = [
        'all' => 'All Types',
        'full-time' => 'Full Time',
        'part-time' => 'Part Time',
        'contract' => 'Contract',
        'freelance' => 'Freelance',
        'internship' => 'Internship'
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function render()
    {
        $jobPostings = JobPosting::with(['employer'])
            ->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhere('location', 'like', '%' . $this->search . '%')
                      ->orWhereHas('employer', function ($q) {
                          $q->where('company_name', 'like', '%' . $this->search . '%');
                      });
            })
            ->when($this->statusFilter !== 'all', function ($query) {
                switch ($this->statusFilter) {
                    case 'active':
                        $query->where('is_active', true)
                              ->where('is_banned', false)
                              ->where(function ($q) {
                                  $q->whereNull('application_deadline')
                                    ->orWhere('application_deadline', '>=', now());
                              });
                        break;
                    case 'inactive':
                        $query->where('is_active', false)
                              ->where('is_banned', false);
                        break;
                    case 'expired':
                        $query->where('application_deadline', '<', now())
                              ->where('is_banned', false);
                        break;
                    case 'banned':
                        $query->where('is_banned', true);
                        break;
                }
            })
            ->when($this->locationFilter, function ($query) {
                $query->where('location', 'like', '%' . $this->locationFilter . '%');
            })
            ->when($this->jobTypeFilter !== 'all', function ($query) {
                $query->where('job_type', $this->jobTypeFilter);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.job-posting-monitor', [
            'jobPostings' => $jobPostings
        ])->layout('components.layouts.app', ['title' => 'Job Posting Monitor']);
    }

    // Custom pagination methods
    public function goToPage($page)
    {
        $this->setPage($page);
    }

    public function goPreviousPage()
    {
        $this->previousPage();
    }

    public function goNextPage()
    {
        $this->nextPage();
    }

    public function showJobDetails($jobId)
    {
        $this->selectedJob = JobPosting::with(['employer'])->findOrFail($jobId);
        $this->showJobModal = true;
        $this->flashEffect = true;
        
        // Reset flash effect after animation completes
        $this->dispatch('reset-flash-effect');
    }

    public function closeJobModal()
    {
        $this->showJobModal = false;
        $this->selectedJob = null;
        $this->flashEffect = false;
    }

    public function toggleJobStatus($jobId)
    {
        try {
            $job = JobPosting::findOrFail($jobId);
            $job->update(['is_active' => !$job->is_active]);
            
            $status = $job->is_active ? 'activated' : 'deactivated';
            session()->flash('message', "Job posting {$status} successfully.");
            
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while updating job status.');
        }
    }

    public function activateJob($jobId)
    {
        try {
            $job = JobPosting::findOrFail($jobId);
            $job->update(['is_active' => true]);
            
            session()->flash('message', 'Job posting activated successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while activating job.');
        }
    }

    public function deactivateJob($jobId)
    {
        try {
            $job = JobPosting::findOrFail($jobId);
            $job->update(['is_active' => false]);

            session()->flash('message', 'Job posting deactivated successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while deactivating job.');
        }
    }

    public function confirmBanJob($jobId)
    {
        $this->jobToBan = JobPosting::findOrFail($jobId);
        $this->banReason = '';
        $this->showBanModal = true;
        $this->confirmingBan = true;
    }

    public function banJob()
    {
        if (!$this->jobToBan || !$this->banReason) {
            session()->flash('error', 'Please provide a reason for banning this job posting.');
            return;
        }

        try {
            \Log::info('Banning job posting', [
                'job_id' => $this->jobToBan->id,
                'reason' => $this->banReason
            ]);

            $this->jobToBan->update([
                'is_banned' => true,
                'banned_reason' => $this->banReason,
                'banned_at' => now(),
                'is_active' => false, // Also deactivate when banned
            ]);

            session()->flash('message', 'Job posting banned successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');
            $this->closeBanModal();

        } catch (\Exception $e) {
            \Log::error('Error banning job posting: ' . $e->getMessage(), [
                'job_id' => $this->jobToBan->id ?? null,
                'exception' => $e
            ]);
            session()->flash('error', 'An error occurred while banning the job posting.');
        }
    }

    public function unbanJob($jobId)
    {
        try {
            $job = JobPosting::findOrFail($jobId);

            \Log::info('Unbanning job posting', ['job_id' => $job->id]);

            $job->update([
                'is_banned' => false,
                'banned_reason' => null,
                'banned_at' => null,
            ]);

            session()->flash('message', 'Job posting unbanned successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            \Log::error('Error unbanning job posting: ' . $e->getMessage(), [
                'job_id' => $jobId,
                'exception' => $e
            ]);
            session()->flash('error', 'An error occurred while unbanning the job posting.');
        }
    }

    public function closeBanModal()
    {
        $this->showBanModal = false;
        $this->jobToBan = null;
        $this->banReason = '';
        $this->confirmingBan = false;
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedLocationFilter()
    {
        $this->resetPage();
    }

    public function updatedJobTypeFilter()
    {
        $this->resetPage();
    }

    public function getJobStatusProperty()
    {
        if (!$this->selectedJob) return 'Unknown';

        if ($this->selectedJob->is_banned) {
            return 'Banned';
        }

        if (!$this->selectedJob->is_active) {
            return 'Inactive';
        }

        if ($this->selectedJob->application_deadline && $this->selectedJob->application_deadline < now()) {
            return 'Expired';
        }

        return 'Active';
    }

    public function getSalaryRangeProperty()
    {
        if (!$this->selectedJob) return 'Not specified';
        
        if ($this->selectedJob->hide_salary || !$this->selectedJob->is_salary_displayed) {
            return 'Not disclosed';
        }
        
        $currency = $this->selectedJob->currency ?? 'IDR';
        $minSalary = $this->selectedJob->min_salary ? number_format($this->selectedJob->min_salary, 0) : null;
        $maxSalary = $this->selectedJob->max_salary ? number_format($this->selectedJob->max_salary, 0) : null;
        
        if ($minSalary && $maxSalary) {
            return "{$currency} {$minSalary} - {$maxSalary}";
        } elseif ($minSalary) {
            return "{$currency} {$minSalary}+";
        } elseif ($maxSalary) {
            return "Up to {$currency} {$maxSalary}";
        }
        
        return 'Not specified';
    }

    public function getUniqueLocations()
    {
        return JobPosting::select('location')
            ->whereNotNull('location')
            ->where('location', '!=', '')
            ->distinct()
            ->orderBy('location')
            ->pluck('location')
            ->take(20);
    }
}
