<?php

namespace App\Livewire;

use App\Models\PertanyaanPreferensi as PertanyaanPreferensiModel;
use App\Models\JawabanPreferensi;
use Livewire\Component;
use Livewire\WithPagination;

class PertanyaanPreferensi extends Component
{
    use WithPagination;

    // Form properties
    public $pertanyaan_id;
    public $teks_pertanyaan = '';
    public $tipe_pertanyaan = '';
    public $kategori = '';
    public $status = true;
    public $urutan = 1;

    // UI state properties
    public $showForm = false;
    public $editMode = false;
    public $search = '';
    public $confirmingDeletion = false;
    public $pertanyaanToDelete = null;
    public $flashEffect = false;

    // Answer management properties
    public $expandedRows = [];
    public $showAnswerForm = [];
    public $editingAnswer = [];
    public $newAnswerText = [];
    public $editAnswerText = [];
    public $confirmingAnswerDeletion = false;
    public $answerToDelete = null;

    // Validation rules
    protected $rules = [
        'teks_pertanyaan' => 'required|string|max:500',
        'tipe_pertanyaan' => 'required|string|max:100',
        'kategori' => 'required|string|max:100',
        'status' => 'boolean',
        'urutan' => 'required|integer|min:1',
    ];

    protected $messages = [
        'teks_pertanyaan.required' => 'Teks pertanyaan is required.',
        'teks_pertanyaan.max' => 'Teks pertanyaan cannot exceed 500 characters.',
        'tipe_pertanyaan.required' => 'Tipe pertanyaan is required.',
        'tipe_pertanyaan.max' => 'Tipe pertanyaan cannot exceed 100 characters.',
        'kategori.required' => 'Kategori is required.',
        'kategori.max' => 'Kategori cannot exceed 100 characters.',
        'urutan.required' => 'Urutan is required.',
        'urutan.integer' => 'Urutan must be a number.',
        'urutan.min' => 'Urutan must be at least 1.',
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function render()
    {
        $pertanyaan = PertanyaanPreferensiModel::with('jawaban')
            ->when($this->search, function ($query) {
                $query->where('teks_pertanyaan', 'like', '%' . $this->search . '%')
                      ->orWhere('tipe_pertanyaan', 'like', '%' . $this->search . '%')
                      ->orWhere('kategori', 'like', '%' . $this->search . '%');
            })
            ->orderBy('urutan')
            ->orderBy('teks_pertanyaan')
            ->paginate(10);

        return view('livewire.pertanyaan-preferensi', [
            'pertanyaan' => $pertanyaan
        ])->layout('components.layouts.app', ['title' => 'Pertanyaan Preferensi Management']);
    }

    public function showCreateForm()
    {
        $this->resetForm();
        $this->showForm = true;
        $this->editMode = false;
        $this->flashEffect = false;
    }

    public function showEditForm($pertanyaanId)
    {
        $pertanyaan = PertanyaanPreferensiModel::findOrFail($pertanyaanId);

        $this->pertanyaan_id = $pertanyaan->id;
        $this->teks_pertanyaan = $pertanyaan->teks_pertanyaan;
        $this->tipe_pertanyaan = $pertanyaan->tipe_pertanyaan;
        $this->kategori = $pertanyaan->kategori;
        $this->status = $pertanyaan->status ?? true;
        $this->urutan = $pertanyaan->urutan ?? 1;

        $this->showForm = true;
        $this->editMode = true;
        $this->flashEffect = true;

        // Reset flash effect after animation completes
        $this->dispatch('reset-flash-effect');
    }

    public function save()
    {
        $this->validate();

        try {
            if ($this->editMode) {
                $pertanyaan = PertanyaanPreferensiModel::findOrFail($this->pertanyaan_id);
                $pertanyaan->update([
                    'teks_pertanyaan' => $this->teks_pertanyaan,
                    'tipe_pertanyaan' => $this->tipe_pertanyaan,
                    'kategori' => $this->kategori,
                    'status' => $this->status,
                    'urutan' => $this->urutan,
                ]);

                session()->flash('message', 'Pertanyaan updated successfully.');
            } else {
                PertanyaanPreferensiModel::create([
                    'teks_pertanyaan' => $this->teks_pertanyaan,
                    'tipe_pertanyaan' => $this->tipe_pertanyaan,
                    'kategori' => $this->kategori,
                    'status' => $this->status,
                    'urutan' => $this->urutan,
                ]);

                session()->flash('message', 'Pertanyaan created successfully.');
            }

            $this->resetForm();
            $this->showForm = false;

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while saving the pertanyaan: ' . $e->getMessage());
        }
    }

    public function confirmDelete($pertanyaanId)
    {
        $this->pertanyaanToDelete = PertanyaanPreferensiModel::findOrFail($pertanyaanId);
        $this->confirmingDeletion = true;
    }

    public function delete()
    {
        if ($this->pertanyaanToDelete) {
            try {
                // Delete related jawaban first
                JawabanPreferensi::where('id_pertanyaan', $this->pertanyaanToDelete->id)->delete();

                // Then delete the pertanyaan
                $this->pertanyaanToDelete->delete();
                session()->flash('message', 'Pertanyaan and related answers deleted successfully.');
            } catch (\Exception $e) {
                session()->flash('error', 'An error occurred while deleting the pertanyaan.');
            }
        }

        $this->confirmingDeletion = false;
        $this->pertanyaanToDelete = null;
    }

    public function cancelDelete()
    {
        $this->confirmingDeletion = false;
        $this->pertanyaanToDelete = null;
    }

    public function cancel()
    {
        $this->resetForm();
        $this->showForm = false;
    }

    public function resetForm()
    {
        $this->pertanyaan_id = null;
        $this->teks_pertanyaan = '';
        $this->tipe_pertanyaan = '';
        $this->kategori = '';
        $this->status = true;
        $this->urutan = 1;
        $this->editMode = false;
        $this->flashEffect = false;
        $this->resetErrorBag();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    // Answer management methods
    public function toggleRow($pertanyaanId)
    {
        if (in_array($pertanyaanId, $this->expandedRows)) {
            $this->expandedRows = array_diff($this->expandedRows, [$pertanyaanId]);
        } else {
            $this->expandedRows[] = $pertanyaanId;
        }
    }

    public function showAddAnswerForm($pertanyaanId)
    {
        $this->showAnswerForm[$pertanyaanId] = true;
        $this->newAnswerText[$pertanyaanId] = '';
    }

    public function hideAddAnswerForm($pertanyaanId)
    {
        unset($this->showAnswerForm[$pertanyaanId]);
        unset($this->newAnswerText[$pertanyaanId]);
    }

    public function saveNewAnswer($pertanyaanId)
    {
        $this->validate([
            "newAnswerText.{$pertanyaanId}" => 'required|string|max:500',
        ], [
            "newAnswerText.{$pertanyaanId}.required" => 'Answer text is required.',
            "newAnswerText.{$pertanyaanId}.max" => 'Answer text cannot exceed 500 characters.',
        ]);

        try {
            JawabanPreferensi::create([
                'id_pertanyaan' => $pertanyaanId,
                'teks_jawaban' => $this->newAnswerText[$pertanyaanId],
            ]);

            session()->flash('message', 'Answer added successfully.');
            $this->hideAddAnswerForm($pertanyaanId);

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while saving the answer.');
        }
    }

    public function editAnswer($answerId)
    {
        $answer = JawabanPreferensi::findOrFail($answerId);
        $this->editingAnswer[$answerId] = true;
        $this->editAnswerText[$answerId] = $answer->teks_jawaban;
    }

    public function cancelEditAnswer($answerId)
    {
        unset($this->editingAnswer[$answerId]);
        unset($this->editAnswerText[$answerId]);
    }

    public function updateAnswer($answerId)
    {
        $this->validate([
            "editAnswerText.{$answerId}" => 'required|string|max:500',
        ], [
            "editAnswerText.{$answerId}.required" => 'Answer text is required.',
            "editAnswerText.{$answerId}.max" => 'Answer text cannot exceed 500 characters.',
        ]);

        try {
            $answer = JawabanPreferensi::findOrFail($answerId);
            $answer->update([
                'teks_jawaban' => $this->editAnswerText[$answerId],
            ]);

            session()->flash('message', 'Answer updated successfully.');
            $this->cancelEditAnswer($answerId);

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while updating the answer.');
        }
    }

    public function confirmDeleteAnswer($answerId)
    {
        $this->answerToDelete = JawabanPreferensi::findOrFail($answerId);
        $this->confirmingAnswerDeletion = true;
    }

    public function deleteAnswer()
    {
        if ($this->answerToDelete) {
            try {
                $this->answerToDelete->delete();
                session()->flash('message', 'Answer deleted successfully.');
            } catch (\Exception $e) {
                session()->flash('error', 'An error occurred while deleting the answer.');
            }
        }

        $this->confirmingAnswerDeletion = false;
        $this->answerToDelete = null;
    }

    public function cancelDeleteAnswer()
    {
        $this->confirmingAnswerDeletion = false;
        $this->answerToDelete = null;
    }
}
