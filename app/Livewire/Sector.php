<?php

namespace App\Livewire;

use App\Models\Sector as SectorModel;
use Livewire\Component;
use Livewire\WithPagination;

class Sector extends Component
{
    use WithPagination;

    // Form properties
    public $sector_id;
    public $sector = '';
    public $sector_desc = '';
    public $is_active = true;

    // UI state properties
    public $showForm = false;
    public $editMode = false;
    public $search = '';
    public $confirmingDeletion = false;
    public $sectorToDelete = null;
    public $flashEffect = false;

    // Validation rules
    protected $rules = [
        'sector' => 'required|string|max:250',
        'sector_desc' => 'nullable|string',
        'is_active' => 'boolean',
    ];

    protected $messages = [
        'sector.required' => 'Sector name is required.',
        'sector.max' => 'Sector name cannot exceed 250 characters.',
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function render()
    {
        $sectors = SectorModel::query()
            ->when($this->search, function ($query) {
                $query->where('sector', 'like', '%' . $this->search . '%')
                      ->orWhere('sector_desc', 'like', '%' . $this->search . '%');
            })
            ->orderBy('sector')
            ->paginate(10);

        return view('livewire.sector', [
            'sectors' => $sectors
        ])->layout('components.layouts.app', ['title' => 'Sector Management']);
    }

    // Custom pagination methods
    public function goToPage($page)
    {
        $this->setPage($page);
    }

    public function goPreviousPage()
    {
        $this->previousPage();
    }

    public function goNextPage()
    {
        $this->nextPage();
    }

    public function showCreateForm()
    {
        $this->resetForm();
        $this->showForm = true;
        $this->editMode = false;
        $this->flashEffect = false;
    }

    public function showEditForm($sectorId)
    {
        $sector = SectorModel::findOrFail($sectorId);

        $this->sector_id = $sector->sector_id;
        $this->sector = $sector->sector;
        $this->sector_desc = $sector->sector_desc;
        $this->is_active = $sector->is_active ?? true;

        $this->showForm = true;
        $this->editMode = true;
        $this->flashEffect = true;

        // Reset flash effect after animation completes
        $this->dispatch('reset-flash-effect');
    }

    public function save()
    {
        $this->validate();

        try {
            if ($this->editMode) {
                $sector = SectorModel::findOrFail($this->sector_id);
                $sector->update([
                    'sector' => $this->sector,
                    'sector_desc' => $this->sector_desc,
                    'is_active' => $this->is_active,
                ]);

                session()->flash('message', 'Sector updated successfully.');
            } else {
                SectorModel::create([
                    'sector' => $this->sector,
                    'sector_desc' => $this->sector_desc,
                    'is_active' => $this->is_active,
                ]);

                session()->flash('message', 'Sector created successfully.');
            }

            $this->resetForm();
            $this->showForm = false;

        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'UNIQUE constraint failed') ||
                str_contains($e->getMessage(), 'Duplicate entry')) {
                $this->addError('sector', 'This sector name already exists.');
            } else {
                session()->flash('error', 'An error occurred while saving the sector.');
            }
        }
    }

    public function confirmDelete($sectorId)
    {
        $this->sectorToDelete = SectorModel::findOrFail($sectorId);
        $this->confirmingDeletion = true;
    }

    public function delete()
    {
        if ($this->sectorToDelete) {
            try {
                $this->sectorToDelete->delete();
                session()->flash('message', 'Sector deleted successfully.');
            } catch (\Exception $e) {
                session()->flash('error', 'An error occurred while deleting the sector.');
            }
        }

        $this->confirmingDeletion = false;
        $this->sectorToDelete = null;
    }

    public function cancelDelete()
    {
        $this->confirmingDeletion = false;
        $this->sectorToDelete = null;
    }

    public function cancel()
    {
        $this->resetForm();
        $this->showForm = false;
    }

    public function resetForm()
    {
        $this->sector_id = null;
        $this->sector = '';
        $this->sector_desc = '';
        $this->is_active = true;
        $this->editMode = false;
        $this->flashEffect = false;
        $this->resetErrorBag();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }
}
