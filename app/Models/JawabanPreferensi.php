<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JawabanPreferensi extends Model
{
    protected $table = 'jawaban';

    protected $fillable = [
        'id_pertanyaan',
        'teks_jawaban',
    ];

    public $timestamps = false; // Ubah menjadi true jika Anda punya kolom created_at & updated_at

    public function pertanyaan(): BelongsTo
    {
        return $this->belongsTo(PertanyaanPreferensi::class, 'id_pertanyaan');
    }
}
