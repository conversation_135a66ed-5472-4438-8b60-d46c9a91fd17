<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PertanyaanPreferensi extends Model
{
    protected $table = 'pertanyaan';
    protected $fillable = [
        'teks_pertanyaan',
        'tipe_pertanyaan',
        'kategori',
        'status',
        'urutan',
    ];

    protected $casts = [
        'status' => 'boolean', // Mengubah 'status' (0 atau 1) menjadi true/false
        'urutan' => 'integer', // Memastikan 'urutan' selalu integer
    ];

    public $timestamps = false;

    public function jawaban(): HasMany
    {
        return $this->hasMany(JawabanPreferensi::class, 'id_pertanyaan');
    }
}
