<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VerificationDocumentHistory extends Model
{
    use HasFactory;
    
    protected $table = 'verification_documents_history';
    
    const CREATED_AT = 'upload_at';
    const UPDATED_AT = null;

    // Document verification status constants (matching VerificationDocument)
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_INCOMPLETE = 'incomplete';
    const STATUS_MISSING = 'missing';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';

    /**
     * Get all available status values
     *
     * @return array
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_PENDING => 'Pending Verification',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_INCOMPLETE => 'Incomplete Documents',
            self::STATUS_MISSING => 'Missing Documents',
            self::STATUS_SUBMITTED => 'Submitted',
            self::STATUS_UNDER_REVIEW => 'Under Review',
        ];
    }

    /**
     * Get status display name
     *
     * @param string $status
     * @return string
     */
    public static function getStatusDisplayName($status)
    {
        $options = self::getStatusOptions();
        return $options[$status] ?? ucfirst($status);
    }
    
    protected $fillable = [
        'verification_document_id',
        'file_path',
        'status',
        'notes',
        'verified_by',
        'verified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'verified_at' => 'datetime',
    ];

    /**
     * Get the parent verification document record.
     */
    public function verificationDocument(): BelongsTo
    {
        return $this->belongsTo(VerificationDocument::class, 'verification_document_id');
    }

    /**
     * Get the user who verified this document upload.
     */
    public function verifiedBy(): BelongsTo
    {
        // Asumsi model User ada di App\Models\User
        return $this->belongsTo(User::class, 'verified_by');
    }
}
