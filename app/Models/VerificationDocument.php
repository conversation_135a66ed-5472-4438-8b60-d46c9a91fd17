<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VerificationDocument extends Model
{
    use HasFactory;
    
    protected $table = 'verification_documents';
    
    const CREATED_AT = null;
    const UPDATED_AT = null;

    // Document verification status constants
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_INCOMPLETE = 'incomplete';
    const STATUS_MISSING = 'missing';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';

    /**
     * Get all available status values
     *
     * @return array
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_PENDING => 'Pending Verification',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_INCOMPLETE => 'Incomplete Documents',
            self::STATUS_MISSING => 'Missing Documents',
            self::STATUS_SUBMITTED => 'Submitted',
            self::STATUS_UNDER_REVIEW => 'Under Review',
        ];
    }

    /**
     * Get status display name
     *
     * @param string $status
     * @return string
     */
    public static function getStatusDisplayName($status)
    {
        $options = self::getStatusOptions();
        return $options[$status] ?? ucfirst($status);
    }
    
    protected $fillable = [
        'employer_id',
        'document_type',
        'file_path',
        'status',
        'submission_date',
        'verification_date',
        'notes',
        'verified_by',
        'current_history_id',
    ];

    /**
     * Get the employer that this document status belongs to.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class, 'employer_id');
    }

    /**
     * Get all of the upload histories for this document.
     */
    public function histories(): HasMany
    {
        return $this->hasMany(VerificationDocumentHistory::class, 'verification_document_id');
    }

    /**
     * Get the currently active/approved history record for this document.
     */
    public function currentHistory(): BelongsTo
    {
        return $this->belongsTo(VerificationDocumentHistory::class, 'current_history_id');
    }
}
