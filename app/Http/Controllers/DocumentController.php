<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use App\Models\VerificationDocumentHistory;

class DocumentController extends Controller
{
    /**
     * Serve verification document files securely
     *
     * @param Request $request
     * @param int $historyId
     * @return Response
     */
    public function serveDocument(Request $request, $historyId)
    {
        try {
            // Find the document history record
            $documentHistory = VerificationDocumentHistory::findOrFail($historyId);
            
            // Check if file exists
            $filePath = $documentHistory->file_path;
            if (!$filePath || !Storage::disk('public')->exists($filePath)) {
                abort(404, 'Document not found');
            }
            
            // Get file info
            $fullPath = Storage::disk('public')->path($filePath);
            $mimeType = Storage::disk('public')->mimeType($filePath);
            $fileName = basename($filePath);
            
            // Security check - ensure file is in allowed directory
            $allowedPath = storage_path('app/public/uploads/documents/');
            $realPath = realpath($fullPath);
            
            if (!$realPath || strpos($realPath, $allowedPath) !== 0) {
                abort(403, 'Access denied');
            }
            
            // Determine if file should be displayed inline or downloaded
            $inline = $request->get('inline', false);
            $disposition = $inline ? 'inline' : 'attachment';
            
            // Return file response
            return response()->file($fullPath, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => $disposition . '; filename="' . $fileName . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
            
        } catch (\Exception $e) {
            abort(404, 'Document not found');
        }
    }

    /**
     * Get document info for preview
     *
     * @param int $historyId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDocumentInfo($historyId)
    {
        try {
            $documentHistory = VerificationDocumentHistory::with('verificationDocument')->findOrFail($historyId);
            
            $filePath = $documentHistory->file_path;
            if (!$filePath || !Storage::disk('public')->exists($filePath)) {
                return response()->json(['error' => 'Document not found'], 404);
            }
            
            $fileSize = Storage::disk('public')->size($filePath);
            $mimeType = Storage::disk('public')->mimeType($filePath);
            $fileName = basename($filePath);
            $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            
            // Determine document type
            $documentType = $this->getDocumentType($extension);
            
            return response()->json([
                'id' => $documentHistory->id,
                'file_name' => $fileName,
                'file_size' => $fileSize,
                'mime_type' => $mimeType,
                'extension' => $extension,
                'document_type' => $documentType,
                'can_preview' => in_array($documentType, ['pdf', 'image']),
                'uploaded_at' => $documentHistory->uploaded_at,
                'verification_document' => [
                    'id' => $documentHistory->verificationDocument->id,
                    'document_type' => $documentHistory->verificationDocument->document_type,
                    'status' => $documentHistory->verificationDocument->status,
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json(['error' => 'Document not found'], 404);
        }
    }

    /**
     * Determine document type based on extension
     *
     * @param string $extension
     * @return string
     */
    private function getDocumentType($extension)
    {
        switch ($extension) {
            case 'pdf':
                return 'pdf';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'webp':
                return 'image';
            case 'doc':
            case 'docx':
                return 'document';
            case 'xls':
            case 'xlsx':
                return 'spreadsheet';
            case 'txt':
                return 'text';
            default:
                return 'unknown';
        }
    }

    /**
     * Get human readable file size
     *
     * @param int $bytes
     * @return string
     */
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
