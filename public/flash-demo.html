<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flash Effect Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            background: #ece9d8;
            margin: 20px;
            padding: 20px;
        }

        .winforms-groupbox {
            border: 2px inset #c0c0c0;
            background: #ece9d8;
            padding: 12px;
            margin-bottom: 12px;
            position: relative;
            width: 400px;
        }

        .winforms-groupbox-title {
            position: absolute;
            top: -8px;
            left: 8px;
            background: #ece9d8;
            padding: 0 4px;
            font-weight: bold;
            font-size: 11px;
            color: #333;
        }

        .winforms-textbox {
            border: 2px inset #c0c0c0;
            background: white;
            padding: 2px 4px;
            font-size: 11px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 8px;
        }

        .winforms-button-primary {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px outset #0078d4;
            color: white;
            padding: 4px 12px;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
        }

        .winforms-button-primary:hover {
            background: linear-gradient(to bottom, #5aa6ff, #106ebe);
        }

        /* Edit Form Flash Effect */
        .winforms-form-flash {
            animation: winforms-edit-flash 0.8s ease-in-out;
        }

        @keyframes winforms-edit-flash {
            0% {
                border: 2px inset #c0c0c0;
                background: #ece9d8;
                box-shadow: none;
            }
            15% {
                border: 2px solid #4a9eff;
                background: #f0f8ff;
                box-shadow: 0 0 8px rgba(74, 158, 255, 0.4), inset 0 0 8px rgba(74, 158, 255, 0.1);
            }
            30% {
                border: 2px solid #0078d4;
                background: #e6f3ff;
                box-shadow: 0 0 12px rgba(0, 120, 212, 0.5), inset 0 0 12px rgba(0, 120, 212, 0.15);
            }
            45% {
                border: 2px solid #4a9eff;
                background: #f0f8ff;
                box-shadow: 0 0 8px rgba(74, 158, 255, 0.4), inset 0 0 8px rgba(74, 158, 255, 0.1);
            }
            60% {
                border: 2px solid #87ceeb;
                background: #f8fcff;
                box-shadow: 0 0 6px rgba(135, 206, 235, 0.3), inset 0 0 6px rgba(135, 206, 235, 0.1);
            }
            100% {
                border: 2px inset #c0c0c0;
                background: #ece9d8;
                box-shadow: none;
            }
        }

        /* Subtle pulse effect for form title during flash */
        .winforms-form-title-flash {
            animation: winforms-title-pulse 0.8s ease-in-out;
        }

        @keyframes winforms-title-pulse {
            0%, 100% {
                color: #333;
                text-shadow: none;
            }
            30% {
                color: #0078d4;
                text-shadow: 0 0 2px rgba(0, 120, 212, 0.3);
            }
        }

        .demo-section {
            margin-bottom: 30px;
        }

        h2 {
            color: #333;
            font-size: 14px;
            margin-bottom: 10px;
        }

        p {
            color: #666;
            line-height: 1.4;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>WinForms Flash Effect Demo</h1>
    
    <div class="demo-section">
        <h2>Normal Form (No Flash Effect)</h2>
        <p>This is how the form looks normally:</p>
        <div class="winforms-groupbox" id="normalForm">
            <div class="winforms-groupbox-title">Add New Sector</div>
            <input type="text" class="winforms-textbox" placeholder="Sector Name" value="">
            <input type="text" class="winforms-textbox" placeholder="Description" value="">
        </div>
    </div>

    <div class="demo-section">
        <h2>Edit Form with Flash Effect</h2>
        <p>Click the button below to see the flash effect when transitioning to edit mode:</p>
        <button class="winforms-button-primary" onclick="triggerFlashEffect()">
            Trigger Edit Flash Effect
        </button>
        
        <div class="winforms-groupbox" id="flashForm">
            <div class="winforms-groupbox-title" id="flashTitle">Edit Sector</div>
            <input type="text" class="winforms-textbox" placeholder="Sector Name" value="Technology">
            <input type="text" class="winforms-textbox" placeholder="Description" value="Information Technology and Software Development">
        </div>
    </div>

    <div class="demo-section">
        <h2>Effect Description</h2>
        <p><strong>What happens when you click "Trigger Edit Flash Effect":</strong></p>
        <ul style="color: #666; line-height: 1.6;">
            <li>The form container gets a blue border glow that pulses</li>
            <li>The background color subtly changes from gray to light blue and back</li>
            <li>The form title gets a subtle color pulse effect</li>
            <li>The inputs get a slight vibration effect</li>
            <li>The entire animation lasts 0.8 seconds</li>
            <li>This provides immediate visual feedback that the form is now in edit mode</li>
        </ul>
    </div>

    <script>
        function triggerFlashEffect() {
            const form = document.getElementById('flashForm');
            const title = document.getElementById('flashTitle');
            
            // Remove classes if they exist
            form.classList.remove('winforms-form-flash');
            title.classList.remove('winforms-form-title-flash');
            
            // Force reflow
            form.offsetHeight;
            
            // Add flash effect classes
            form.classList.add('winforms-form-flash');
            title.classList.add('winforms-form-title-flash');
            
            // Add subtle vibration effect to inputs
            const inputs = form.querySelectorAll('.winforms-textbox');
            inputs.forEach(input => {
                input.style.transition = 'all 0.1s ease-in-out';
                setTimeout(() => {
                    input.style.transform = 'translateX(1px)';
                    setTimeout(() => {
                        input.style.transform = 'translateX(-1px)';
                        setTimeout(() => {
                            input.style.transform = 'translateX(0)';
                            input.style.transition = '';
                        }, 50);
                    }, 50);
                }, 200);
            });
            
            // Remove classes after animation completes
            setTimeout(() => {
                form.classList.remove('winforms-form-flash');
                title.classList.remove('winforms-form-title-flash');
            }, 800);
        }
    </script>
</body>
</html>
