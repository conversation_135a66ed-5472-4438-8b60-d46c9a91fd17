<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WinForms Custom Pagination Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            background: #ece9d8;
            margin: 20px;
            padding: 20px;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            font-size: 16px;
            margin-bottom: 20px;
        }

        h2 {
            color: #333;
            font-size: 14px;
            margin: 20px 0 10px 0;
        }

        p {
            color: #666;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        /* Custom WinForms Pagination Styles */
        .winforms-custom-pagination {
            margin-top: 12px;
            padding: 8px;
            background: #ece9d8;
            border: 1px inset #c0c0c0;
        }

        .winforms-pagination-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .winforms-page-info {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 11px;
            color: #333;
            font-weight: bold;
        }

        .winforms-total-records {
            color: #666;
            font-weight: normal;
            font-size: 10px;
        }

        .winforms-pagination-controls {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .winforms-pagination-btn {
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            border: 1px outset #c0c0c0;
            color: #333;
            padding: 3px 8px;
            cursor: pointer;
            font-size: 10px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-width: 24px;
            text-align: center;
            transition: all 0.1s ease;
        }

        .winforms-pagination-btn:hover:not(.disabled):not(.active) {
            background: linear-gradient(to bottom, #ffffff, #e8e8e8);
            border: 1px outset #b0b0b0;
        }

        .winforms-pagination-btn:active:not(.disabled):not(.active) {
            background: linear-gradient(to bottom, #e0e0e0, #f8f8f8);
            border: 1px inset #c0c0c0;
        }

        .winforms-pagination-btn.active {
            background: linear-gradient(to bottom, #4a9eff, #0078d4);
            border: 1px inset #0078d4;
            color: white;
            font-weight: bold;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .winforms-pagination-btn.disabled {
            background: #f0f0f0;
            border: 1px outset #d0d0d0;
            color: #999;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .winforms-pagination-ellipsis {
            color: #666;
            font-size: 10px;
            padding: 3px 4px;
            font-weight: bold;
        }

        .winforms-records-info {
            font-size: 10px;
            color: #666;
            text-align: center;
            padding: 4px 8px;
            background: #f8f8f8;
            border: 1px inset #c0c0c0;
            border-radius: 2px;
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 15px;
            background: #f0f0f0;
            border: 2px inset #c0c0c0;
        }

        .feature-list {
            color: #333;
            line-height: 1.6;
        }

        .feature-list li {
            margin-bottom: 5px;
        }

        .code-example {
            background: #fff;
            border: 1px inset #c0c0c0;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            color: #333;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>WinForms Custom Pagination Demo</h1>
        
        <div class="demo-section">
            <h2>Overview</h2>
            <p>This demonstrates the custom WinForms-styled pagination system that replaces Laravel's default pagination links. The pagination maintains the authentic Visual Basic WinForms aesthetic while providing full functionality.</p>
        </div>

        <div class="demo-section">
            <h2>Features Implemented</h2>
            <ul class="feature-list">
                <li><strong>WinForms Styling:</strong> Gray gradient backgrounds, inset/outset borders, 3D button effects</li>
                <li><strong>Navigation Controls:</strong> First, Previous, Next, Last buttons with proper icons</li>
                <li><strong>Page Numbers:</strong> Current page ± 2 pages with ellipsis for large ranges</li>
                <li><strong>Active State:</strong> Current page highlighted with blue background</li>
                <li><strong>Disabled States:</strong> Proper disabling of navigation buttons at boundaries</li>
                <li><strong>Page Information:</strong> "Page X of Y" and total records display</li>
                <li><strong>Records Info:</strong> "Showing X to Y of Z entries" information</li>
                <li><strong>Responsive Design:</strong> Adapts to different screen sizes</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Example Pagination (Page 3 of 8)</h2>
            <div class="winforms-custom-pagination">
                <div class="winforms-pagination-container">
                    <!-- Page Info -->
                    <div class="winforms-page-info">
                        <span>Page 3 of 8</span>
                        <span class="winforms-total-records">(75 total records)</span>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="winforms-pagination-controls">
                        <!-- First Page Button -->
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to First Page')">
                            <i class="fas fa-angle-double-left" style="font-size: 9px;"></i>
                            First
                        </button>

                        <!-- Previous Page Button -->
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Previous Page')">
                            <i class="fas fa-angle-left" style="font-size: 9px;"></i>
                            Prev
                        </button>

                        <!-- Page Number Buttons -->
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Page 1')">1</button>
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Page 2')">2</button>
                        <button class="winforms-pagination-btn active">3</button>
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Page 4')">4</button>
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Page 5')">5</button>
                        <span class="winforms-pagination-ellipsis">...</span>

                        <!-- Next Page Button -->
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Next Page')">
                            Next
                            <i class="fas fa-angle-right" style="font-size: 9px;"></i>
                        </button>

                        <!-- Last Page Button -->
                        <button class="winforms-pagination-btn" onclick="showMessage('Go to Last Page')">
                            Last
                            <i class="fas fa-angle-double-right" style="font-size: 9px;"></i>
                        </button>
                    </div>

                    <!-- Records Per Page Info -->
                    <div class="winforms-records-info">
                        <span>Showing 21 to 30 of 75 entries</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>Edge Cases Handled</h2>
            <ul class="feature-list">
                <li><strong>First Page:</strong> Previous and First buttons are disabled</li>
                <li><strong>Last Page:</strong> Next and Last buttons are disabled</li>
                <li><strong>Single Page:</strong> No pagination controls shown</li>
                <li><strong>Large Page Ranges:</strong> Ellipsis (...) shown for non-adjacent pages</li>
                <li><strong>Search Integration:</strong> Pagination resets when searching</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Technical Implementation</h2>
            <p><strong>Livewire Methods:</strong></p>
            <div class="code-example">
public function goToPage($page) { $this->setPage($page); }
public function goPreviousPage() { $this->previousPage(); }
public function goNextPage() { $this->nextPage(); }
            </div>
            
            <p><strong>Blade Template Logic:</strong></p>
            <div class="code-example">
@if($sectors->hasPages())
    &lt;div class="winforms-custom-pagination"&gt;
        &lt;!-- Page info, controls, and records info --&gt;
    &lt;/div&gt;
@endif
            </div>
        </div>

        <div class="demo-section">
            <h2>Live Demo</h2>
            <p>Visit the actual Sector management interface to see the custom pagination in action:</p>
            <p><strong>URL:</strong> <a href="http://localhost:8000/sectors" target="_blank">http://localhost:8000/sectors</a></p>
            <p>The pagination will appear when there are more than 10 sectors in the database.</p>
        </div>
    </div>

    <script>
        function showMessage(action) {
            alert('Pagination Action: ' + action + '\n\nThis demonstrates the WinForms-style pagination controls.');
        }
    </script>
</body>
</html>
