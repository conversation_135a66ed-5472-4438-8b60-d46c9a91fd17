<?php

use Illuminate\Support\Facades\Route;

Route::get('/', App\Livewire\Welcome::class)->name('welcome');
Route::get('/sectors', App\Livewire\Sector::class)->name('sectors');
Route::get('/pertanyaan-preferensi', App\Livewire\PertanyaanPreferensi::class)->name('pertanyaan-preferensi');
Route::get('/employer-verification', App\Livewire\EmployerVerification::class)->name('employer-verification');
Route::get('/job-posting-monitor', App\Livewire\JobPostingMonitor::class)->name('job-posting-monitor');

// Document serving routes
Route::get('/documents/{historyId}/serve', [App\Http\Controllers\DocumentController::class, 'serveDocument'])->name('documents.serve');
Route::get('/documents/{historyId}/info', [App\Http\Controllers\DocumentController::class, 'getDocumentInfo'])->name('documents.info');
